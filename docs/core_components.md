# Core Components Documentation

## Overview

The core layer (`app/core/`) provides the foundational components that enable the Actor-based architecture and event-driven communication patterns throughout the Zeitwahl AI Agent system.

## Actor System

### Base Actor Class (`app/core/actors/actor.py`)

The `Actor` class is the foundation of the system's actor-based architecture.

#### Actor States
```python
class ActorState(Enum):
    CREATED = "created"           # Actor instance created
    INITIALIZING = "initializing" # Actor is being initialized
    INITIALIZED = "initialized"   # Actor initialization complete
    RUNNING = "running"          # Actor is active and processing
    STOPPING = "stopping"       # Actor is shutting down
    STOPPED = "stopped"         # Actor has stopped
    ERROR = "error"             # Actor encountered an error
```

#### Key Features
- **Automatic Initialization**: `initialize()` method called automatically on start
- **Lifecycle Management**: Complete start/stop lifecycle with state tracking
- **Event Bus Integration**: Automatic subscription to tagged methods
- **Error Isolation**: Actor failures don't affect other components
- **Dependency Injection**: Services injected during initialization

#### Core Methods
- `initialize()`: Abstract method for actor-specific initialization, called by `start()` and can be overridden
- `cleanup()`: Optional cleanup before stopping
- `start()`: Complete startup process with lifecycle management called by `deploy()` and can be overridden
- `stop()`: Graceful shutdown with cleanup
- `deploy()`: Independent deployment for concurrent startup

#### Usage Pattern
```python
class MyActor(Actor):
    async def initialize(self):
        # Actor-specific initialization
        self.service = SomeService()
        
    @event_bus.subscribe("SomeEvent")
    async def handle_event(self, event):
        # Event handling logic
        pass
```

### Actor System Manager (`app/core/actors/actor_system.py`)

Manages the complete lifecycle of all actors in the system.

#### Key Features
- **Dependency-Ordered Startup**: Actors start in dependency order
- **Graceful Shutdown**: Reverse-order shutdown with cleanup
- **Health Monitoring**: Actor state tracking and health checks
- **Error Recovery**: Isolated error handling per actor

#### Actor Startup Order
1. **DBActor**: Database must be ready first
2. **PreprocessingActor**: Needs database for user/message operations
3. **LLMCallsActor**: Can start independently
4. **BotActor**: Starts last to trigger message flow

#### Core Methods
- `start_all()`: Start all actors in dependency order
- `stop_all()`: Stop all actors in reverse order
- `get_actor_status()`: Get status of all actors
- `restart_actor()`: Restart a specific actor

## Event Bus System

### Event Bus Implementation (`app/core/events/event_bus.py`)

High-performance, thread-safe event bus supporting 500+ events/sec.

#### Key Features
- **Non-blocking Publishing**: Fire-and-forget semantics for high throughput
- **Priority-based Handling**: Handlers can be prioritized
- **Thread-safe Operations**: Concurrent access protection
- **Error Isolation**: Handler failures don't affect other handlers

#### Core Methods
- `publish(event_type, message)`: Asynchronous event publishing
- `subscribe_to_topic(event_type, callback, priority)`: Manual subscription
- `subscribe_tagged_methods(actor)`: Automatic subscription for tagged methods

#### Subscription Patterns
```python
# Manual subscription
await event_bus.subscribe_to_topic("MessageReceived", handler, priority=10)

# Automatic subscription via decorator
@event_bus.subscribe("MessageReceived", priority=5)
async def handle_message(self, event):
    pass
```

#### Performance Characteristics
- **Latency**: Sub-millisecond event publishing
- **Throughput**: 500+ events/sec sustained
- **Memory**: Bounded registry with automatic cleanup
- **Concurrency**: Lock-free fast path for high-frequency events

### Event Definitions (`app/core/events/events.py`)

Comprehensive event catalog for system-wide communication.

#### Core Event Categories

**Message Processing Events**
- `UserMessageReceived`: New message from Telegram
- `UserIdentified`: User validation complete
- `UserRegistered`: New user registration complete
- `ContextBuilt`: Conversation context prepared
- `LLMRequestReady`: Prompt ready for LLM processing
- `LLMResponseGenerated`: LLM response received
- `SendMessageRequest`: Request to send message to user

**Database Events**
- `CreateUserRequest/Response`: User creation operations
- `GetUserByTelegramIdRequest/Response`: User retrieval
- `CreateMessageRequest/Response`: Message storage
- `GetConversationHistoryRequest/Response`: History retrieval

**System Events**
- `ErrorOccurred`: System error reporting
- `UserSessionStarted`: New user session
- `ActorStarted/Stopped`: Actor lifecycle events

#### Event Structure
All events inherit from `BaseEvent` and include:
- **Timestamp**: UTC timestamp for event ordering
- **Context ID**: Correlation ID for message tracking
- **Type-specific Data**: Event payload with validation

## Message Context Management

### Message Context (`app/core/context/message_context.py`)

Provides end-to-end tracking and state management for message processing.

#### MessageContext Class
```python
@dataclass
class MessageContext:
    context_id: str              # Unique identifier
    telegram_user_id: int        # User identification
    chat_id: int                 # Chat identification
    telegram_message_id: int     # Message identification
    message_text: str            # Original message content
    state: MessageState          # Current processing state
    created_at: datetime         # Context creation time
    metadata: Dict[str, Any]     # Additional context data
```

#### Message States
```python
class MessageState(Enum):
    RECEIVED = "received"           # Message received from Telegram
    USER_IDENTIFIED = "user_identified"  # User validation complete
    CONTEXT_BUILT = "context_built"     # Context preparation complete
    LLM_READY = "llm_ready"            # Ready for LLM processing
    LLM_PROCESSING = "llm_processing"   # LLM call in progress
    LLM_COMPLETE = "llm_complete"      # LLM response received
    RESPONSE_SENT = "response_sent"     # Response sent to user
    ERROR = "error"                    # Error occurred
    COMPLETED = "completed"            # Processing complete
```

#### Context Manager
The `MessageContextManager` provides centralized context management:

- **Context Creation**: Generate unique contexts for new messages
- **State Tracking**: Update context state throughout processing
- **Context Retrieval**: Access context by ID across actors
- **Cleanup**: Automatic cleanup of completed contexts
- **Debugging**: Context history for troubleshooting

#### Usage Patterns
```python
# Create new context
context = await message_context_manager.create_context(
    telegram_user_id=user_id,
    chat_id=chat_id,
    telegram_message_id=msg_id,
    message_text=text
)

# Update context state
await message_context_manager.update_context_state(
    context_id, MessageState.USER_IDENTIFIED
)

# Retrieve context
context = await message_context_manager.get_context(context_id)
```

## Integration Patterns

### Actor-Event Bus Integration

Actors automatically integrate with the event bus through:

1. **Automatic Subscription**: Tagged methods are subscribed during actor startup
2. **Event Publishing**: Actors publish events to communicate with other actors
3. **Context Propagation**: All events include context_id for correlation
4. **Error Handling**: Event bus errors are isolated and logged

### Context-Event Integration

Message context is integrated throughout the event flow:

1. **Context Creation**: BotActor creates context for new messages
2. **Context Propagation**: All events include the context_id
3. **State Updates**: Actors update context state as processing progresses
4. **Context Cleanup**: Completed contexts are automatically cleaned up

### Error Handling Integration

The core components provide comprehensive error handling:

1. **Actor Isolation**: Actor errors don't affect other actors
2. **Event Error Handling**: Event bus isolates handler errors
3. **Context Error State**: Contexts track error states and details
4. **Recovery Mechanisms**: Automatic retry and fallback patterns

## Performance Considerations

### Event Bus Performance
- **Lock-free Fast Path**: High-frequency events avoid locks
- **Priority Queuing**: Critical events processed first
- **Bounded Memory**: Registry size limits prevent memory leaks
- **Async Execution**: Non-blocking event processing

### Context Management Performance
- **In-memory Storage**: Fast context access and updates
- **Automatic Cleanup**: Prevents memory accumulation
- **Efficient Lookups**: Hash-based context retrieval
- **Minimal Overhead**: Lightweight context objects

### Actor System Performance
- **Concurrent Startup**: Actors deploy concurrently when possible
- **Independent Scaling**: Actors can be scaled independently
- **Resource Isolation**: Actor resource usage is isolated
- **Graceful Degradation**: System continues with partial actor failures

## Monitoring and Debugging

### Logging Integration
- **Context-aware Logging**: All logs include context_id when available
- **Actor Lifecycle Logging**: Actor state changes are logged
- **Event Flow Logging**: Event publishing and handling is logged
- **Performance Metrics**: Processing times and throughput metrics

### Debug Capabilities
- **Context History**: Complete processing history per message
- **Event Tracing**: Event flow visualization and debugging
- **Actor State Inspection**: Real-time actor status monitoring
- **Error Correlation**: Error tracking across components
