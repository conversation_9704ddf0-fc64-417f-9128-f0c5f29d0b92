# <PERSON><PERSON><PERSON>hl AI Agent - Architecture Overview

## System Overview

Zeitwahl is an AI-powered Telegram bot designed for intelligent calendar scheduling and meeting coordination. The system follows an **Actor-based architecture** with **event-driven communication** and **message context tracking** for reliable, scalable message processing.

## Core Architectural Principles

### 1. Actor-Based Architecture
- **Autonomous Components**: Each actor is an independent, asynchronous component with its own lifecycle
- **Isolation**: Actors communicate only through the event bus, ensuring loose coupling
- **Fault Tolerance**: Actor failures are isolated and don't affect other system components
- **Scalability**: Actors can be deployed independently and scaled based on load

### 2. Event-Driven Communication
- **Decoupled Messaging**: All inter-component communication happens through events
- **Asynchronous Processing**: Non-blocking event publishing with fire-and-forget semantics
- **Priority-Based Handling**: Event handlers can be prioritized for critical operations
- **Event Correlation**: All events include context_id for end-to-end message tracking

### 3. Message Context Management
- **End-to-End Tracking**: Each user message gets a unique context_id for complete traceability
- **State Consistency**: Message processing state is maintained across all actors
- **Debugging Support**: Context tracking enables comprehensive debugging and monitoring

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Zeitwahl AI Agent                        │
├─────────────────────────────────────────────────────────────────┤
│                     Actor System Manager                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ Bot Actor   │  │ DB Actor    │  │ Preprocessing│  │ LLM     │ │
│  │             │  │             │  │ Actor       │  │ Actor   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        Event Bus                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Infrastructure│  │   Domain    │  │    Core     │  │ Config  │ │
│  │   Layer     │  │   Layer     │  │   Layer     │  │ Layer   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Layer Architecture

### Core Layer (`app/core/`)
**Foundation components that provide system-wide functionality**

- **Actor System**: Base actor classes and actor system management
- **Event Bus**: High-performance event publishing and subscription system
- **Message Context**: Context tracking and state management for message processing

### Infrastructure Layer (`app/infrastructure/`)
**External integrations and infrastructure services**

- **Database**: MongoDB integration with models, repositories, and services
- **Telegram**: Bot integration for message handling and user interaction
- **LLM**: Language model API integrations with multiple provider support

### Domain Layer (`app/domain/`)
**Business logic and domain-specific functionality**

- **Preprocessing**: Message validation, context building, and prompt preparation
- **Postprocessing**: Response formatting and enhancement (future)
- **Scheduling**: Calendar integration and meeting coordination logic

### Configuration Layer (`app/config/`)
**Centralized configuration management**

- **Settings**: Environment-based configuration with validation
- **Secrets**: Secure handling of API keys and credentials

## Key Design Patterns

### 1. Dependency Injection
- Actors receive dependencies through constructor injection
- Services are injected into actors during initialization
- Promotes testability and loose coupling

### 2. Repository Pattern
- Data access is abstracted through repository interfaces
- Business logic is separated from data persistence concerns
- Enables easy testing with mock repositories

### 3. Event Sourcing (Partial)
- All significant system events are published to the event bus
- Event history provides audit trail and debugging capabilities
- Enables event replay for testing and debugging

### 4. Command Query Responsibility Segregation (CQRS)
- Database operations are separated into commands (writes) and queries (reads)
- Event bus handles command/response patterns for database operations
- Optimizes performance for different operation types

## Actor Responsibilities

### BotActor (`app/infrastructure/telegram/bot_actor.py`)
- **Primary Role**: Telegram bot interface and message handling
- **Responsibilities**:
  - Receive messages from Telegram API
  - Create message context for each incoming message
  - Publish UserMessageReceived events
  - Send responses back to users
  - Handle pre-defined bot commands and interactions

### DBActor (`app/infrastructure/database/db_actor.py`)
- **Primary Role**: Database operations and data persistence
- **Responsibilities**:
  - Handle all database CRUD operations
  - Manage database connections and transactions
  - Provide data consistency and integrity
  - Handle database-related events and responses

### PreprocessingActor (`app/domain/preprocessing/preprocessing_actor.py`)
- **Primary Role**: Message preprocessing and validation
- **Responsibilities**:
  - Validate incoming messages
  - Decide if LLM should be used to interpret the message
  - Extract context and build conversation history
  - Prepare prompts for LLM processing
  - Filter and sanitize user input

### LLMCallsActor (`app/infrastructure/llm/llm_calls_actor.py`)
- **Primary Role**: Language model integration and response generation
- **Responsibilities**:
  - Interface with multiple LLM providers
  - Handle API calls and response processing
  - Manage token usage and cost tracking
  - Provide fallback mechanisms for provider failures

## Event Flow Architecture

### Message Processing Flow
```
User Message → BotActor → UserMessageReceived Event
                ↓
PreprocessingActor → UserIdentified Event
                ↓
PreprocessingActor → ContextBuilt Event
                ↓
PreprocessingActor → LLMRequestReady Event
                ↓
LLMCallsActor → LLMResponseGenerated Event
                ↓
BotActor → SendMessageRequest Event
                ↓
User receives response
```

### Database Operation Flow
```
Actor → Database Request Event → DBActor
                ↓
Database Operation Execution
                ↓
DBActor → Database Response Event → Requesting Actor
```

## Technology Stack

### Core Technologies
- **Python 3.13+**: Modern Python with async/await support
- **AsyncIO**: Asynchronous programming for high concurrency
- **Pydantic**: Data validation and settings management
- **MongoDB**: Document database for flexible data storage
- **aiogram**: Telegram bot framework for message handling

### External Integrations
- **Telegram Bot API**: User interface and message handling
- **Google Gemini API**: Primary LLM provider
- **Deepseek API**: Fallback LLM provider
- **Google Calendar API**: Calendar integration (future)
- **Microsoft Outlook API**: Calendar integration (future)

### Development Tools
- **pytest**: Testing framework
- **uv**: Fast Python package manager
- **dotenv**: Environment variable management

## Scalability Considerations

### Horizontal Scaling
- Actors can be deployed as separate services
- Event bus can be replaced or complemented with distributed message queue (Redis, RabbitMQ)
- Database can be sharded or replicated

### Performance Optimization
- Event bus supports 500+ events/sec with minimal latency
- Database operations are optimized with proper indexing
- LLM calls include token usage optimization and cost tracking

### Fault Tolerance
- Actor isolation prevents cascading failures
- Event bus includes error handling and retry mechanisms
- Database operations include transaction support and rollback

## Security Architecture

### Data Protection
- User data is encrypted in transit and at rest
- API keys are managed through environment variables
- Database access is controlled through connection pooling

### Input Validation
- All user inputs are validated before processing
- Message content is sanitized to prevent injection attacks
- Rate limiting prevents abuse and DoS attacks

### Authentication & Authorization
- Telegram user authentication through bot tokens
- API key validation for external service access
- Role-based access control for administrative functions

## Monitoring and Observability

### Logging
- Structured logging with emoji indicators for different components
- Context-aware logging with correlation IDs
- Different log levels for development and production

### Metrics
- Token usage tracking for cost management
- Message processing time monitoring
- Actor performance and health metrics

### Error Handling
- Comprehensive error capture and reporting
- Graceful degradation for service failures
- Automatic retry mechanisms for transient failures
- Handling of specific errors instead of a general Exception caught
