0-# Configuration Management Documentation

## Overview

The configuration system (`app/config/`) provides centralized, environment-aware configuration management for the Zeitwahl AI Agent. It uses Pydantic for validation and supports multiple environments with secure credential handling.

## Configuration Architecture

### Settings Structure (`app/config/settings/settings.py`)

The configuration is organized into logical sections, each with its own configuration class:

```python
class Settings:
    def __init__(self):
        self.app = AppConfig()           # Application settings
        self.telegram = TelegramConfig() # Telegram bot configuration
        self.llm = LLMConfig()          # LLM provider settings
        self.database = DatabaseConfig() # Database connection settings
        self.calendar = CalendarConfig() # Calendar integration settings
```

### Configuration Classes

#### AppConfig
**Purpose**: Core application settings and behavior configuration

**Key Settings:**
- `environment: str` - Application environment (development, staging, production)
- `debug: bool` - Debug mode toggle
- `log_level: str` - Logging verbosity level
- `rate_limit_requests: int` - Rate limiting configuration
- `rate_limit_window: int` - Rate limiting time window
- `secret_key: str` - Application secret for security operations

**Environment Variables:**
- `APP_ENVIRONMENT` - Runtime environment
- `APP_DEBUG` - Debug mode flag
- `APP_LOG_LEVEL` - Logging level
- `APP_RATE_LIMIT_REQUESTS` - Rate limit requests per window
- `APP_RATE_LIMIT_WINDOW` - Rate limit window in seconds
- `APP_SECRET_KEY` - Application secret key

#### TelegramConfig
**Purpose**: Telegram bot integration configuration

**Key Settings:**
- `bot_token: str` - Telegram bot token from @BotFather
- `webhook_url: Optional[str]` - Webhook URL for production
- `webhook_secret: Optional[str]` - Webhook secret for security
- `use_webhook: bool` - Webhook vs polling mode
- `polling_timeout: int` - Polling timeout in seconds
- `max_connections: int` - Maximum webhook connections
- `allowed_updates: List[str]` - Allowed update types

**Environment Variables:**
- `TELEGRAM_BOT_TOKEN` - Bot token (required)
- `TELEGRAM_WEBHOOK_URL` - Webhook URL
- `TELEGRAM_WEBHOOK_SECRET` - Webhook secret
- `TELEGRAM_USE_WEBHOOK` - Webhook mode flag
- `TELEGRAM_POLLING_TIMEOUT` - Polling timeout
- `TELEGRAM_MAX_CONNECTIONS` - Connection limit

#### LLMConfig
**Purpose**: Language model provider configuration

**Key Settings:**
- `primary_provider: str` - Primary LLM provider (gemini, deepseek, mock)
- `fallback_providers: List[str]` - Fallback provider chain
- `gemini_api_key: Optional[str]` - Google Gemini API key
- `deepseek_api_key: Optional[str]` - Deepseek API key
- `deepseek_base_url: str` - Deepseek API base URL
- `max_tokens: int` - Maximum tokens per request
- `temperature: float` - Response creativity/randomness
- `timeout: int` - API request timeout

**Environment Variables:**
- `LLM_PRIMARY_PROVIDER` - Primary provider selection
- `LLM_FALLBACK_PROVIDERS` - Comma-separated fallback list
- `LLM_GEMINI_API_KEY` - Gemini API key
- `LLM_DEEPSEEK_API_KEY` - Deepseek API key
- `LLM_DEEPSEEK_BASE_URL` - Deepseek API endpoint
- `LLM_MAX_TOKENS` - Token limit
- `LLM_TEMPERATURE` - Response temperature
- `LLM_TIMEOUT` - Request timeout

#### DatabaseConfig
**Purpose**: Database and caching configuration

**Key Settings:**
- `mongodb_url: str` - MongoDB connection string
- `database_name: str` - Database name
- `redis_url: str` - Redis connection string for caching
- `redis_db: int` - Redis database number
- `cache_ttl: int` - Cache time-to-live in seconds

**Environment Variables:**
- `DB_MONGODB_URL` - MongoDB connection URL
- `DB_DATABASE_NAME` - Database name
- `DB_REDIS_URL` - Redis connection URL
- `DB_REDIS_DB` - Redis database number
- `DB_CACHE_TTL` - Cache TTL in seconds

#### CalendarConfig
**Purpose**: Calendar service integration configuration

**Key Settings:**
- `google_credentials_file: Optional[str]` - Google credentials JSON file path
- `google_client_id: Optional[str]` - Google OAuth client ID
- `google_client_secret: Optional[str]` - Google OAuth client secret
- `outlook_client_id: Optional[str]` - Outlook OAuth client ID
- `outlook_client_secret: Optional[str]` - Outlook OAuth client secret
- `outlook_tenant_id: Optional[str]` - Outlook tenant ID
- `default_timezone: str` - Default timezone for scheduling

**Environment Variables:**
- `CALENDAR_GOOGLE_CREDENTIALS_FILE` - Google credentials file
- `CALENDAR_GOOGLE_CLIENT_ID` - Google OAuth client ID
- `CALENDAR_GOOGLE_CLIENT_SECRET` - Google OAuth secret
- `CALENDAR_OUTLOOK_CLIENT_ID` - Outlook OAuth client ID
- `CALENDAR_OUTLOOK_CLIENT_SECRET` - Outlook OAuth secret
- `CALENDAR_OUTLOOK_TENANT_ID` - Outlook tenant ID
- `CALENDAR_DEFAULT_TIMEZONE` - Default timezone

## Environment Management

### Environment Files

The system supports multiple environment configurations through `.env` files:

**Development (`.env.development`)**:
```env
APP_ENVIRONMENT=development
APP_DEBUG=true
APP_LOG_LEVEL=DEBUG
TELEGRAM_USE_WEBHOOK=false
LLM_PRIMARY_PROVIDER=mock
DB_MONGODB_URL=mongodb://localhost:27017
```

**Production (`.env.production`)**:
```env
APP_ENVIRONMENT=production
APP_DEBUG=false
APP_LOG_LEVEL=INFO
TELEGRAM_USE_WEBHOOK=true
LLM_PRIMARY_PROVIDER=gemini
DB_MONGODB_URL=mongodb+srv://cluster.mongodb.net
```

**Testing (`.env.test`)**:
```env
APP_ENVIRONMENT=test
APP_DEBUG=true
APP_LOG_LEVEL=DEBUG
LLM_PRIMARY_PROVIDER=mock
DB_DATABASE_NAME=zeitwahl_test
```

### Environment Detection

The system automatically detects the environment and loads appropriate settings:

```python
# Environment detection logic
environment = os.getenv("APP_ENVIRONMENT", "development")

# Load environment-specific .env file
env_file = f".env.{environment}"
if os.path.exists(env_file):
    load_dotenv(env_file)
else:
    load_dotenv()  # Load default .env
```

## Configuration Validation

### Pydantic Validation

All configuration classes use Pydantic for automatic validation:

**Type Validation**:
- Automatic type conversion and validation
- Required field enforcement
- Optional field handling with defaults
- Complex type validation (URLs, emails, etc.)

**Custom Validators**:
```python
@field_validator('mongodb_url')
def validate_mongodb_url(cls, v):
    if not v.startswith(('mongodb://', 'mongodb+srv://')):
        raise ValueError('Invalid MongoDB URL format')
    return v

@field_validator('bot_token')
def validate_bot_token(cls, v):
    if not v or len(v) < 40:
        raise ValueError('Invalid Telegram bot token')
    return v
```

### Configuration Validation

The system validates configuration at startup:

```python
async def validate_configuration():
    """Validate all configuration settings."""
    
    # Required settings validation
    if not settings.telegram.bot_token:
        raise ConfigurationError("Telegram bot token is required")
    
    # LLM provider validation
    if settings.llm.primary_provider not in ['gemini', 'deepseek', 'mock']:
        raise ConfigurationError("Invalid primary LLM provider")
    
    # Database connectivity validation
    try:
        await test_database_connection()
    except Exception as e:
        raise ConfigurationError(f"Database connection failed: {e}")
```

## Security Considerations

### Credential Management

**Environment Variables**:
- All sensitive credentials stored in environment variables
- No hardcoded secrets in source code
- Environment-specific credential isolation
- Automatic credential validation

**Secret Rotation**:
- Support for credential rotation without restart
- Graceful handling of expired credentials
- Fallback mechanisms for credential failures
- Audit logging for credential access

### Access Control

**Configuration Access**:
- Read-only access to configuration after initialization
- Immutable configuration objects
- Controlled access to sensitive settings
- Logging of configuration access

**Runtime Security**:
- Secure handling of API keys in memory
- Automatic credential masking in logs
- Secure transmission of credentials to services
- Memory cleanup of sensitive data

## Configuration Usage Patterns

### Service Configuration

Services receive configuration through dependency injection:

```python
class LLMService:
    def __init__(self, config: LLMConfig):
        self.config = config
        self.primary_provider = config.primary_provider
        self.api_keys = {
            'gemini': config.gemini_api_key,
            'deepseek': config.deepseek_api_key
        }
```

### Actor Configuration

Actors access configuration through the global settings object:

```python
class BotActor(Actor):
    async def initialize(self):
        self.bot_token = settings.telegram.bot_token
        self.use_webhook = settings.telegram.use_webhook
        self.webhook_url = settings.telegram.webhook_url
```

### Dynamic Configuration

Some settings can be updated at runtime:

```python
# Update rate limiting
settings.app.rate_limit_requests = new_limit

# Update LLM provider
settings.llm.primary_provider = new_provider

# Notify services of configuration changes
await event_bus.publish("ConfigurationUpdated", {
    "section": "llm",
    "changes": {"primary_provider": new_provider}
})
```

## Configuration Monitoring

### Health Checks

Configuration health is monitored continuously:

```python
async def configuration_health_check():
    """Check configuration health and validity."""
    
    health_status = {
        "telegram": await check_telegram_config(),
        "llm": await check_llm_config(),
        "database": await check_database_config(),
        "calendar": await check_calendar_config()
    }
    
    return health_status
```

### Configuration Metrics

Key configuration metrics are tracked:

- **Credential Expiry**: Monitor API key expiration
- **Service Availability**: Track external service connectivity
- **Configuration Changes**: Log configuration updates
- **Validation Failures**: Monitor configuration errors

## Best Practices

### Development

**Local Development**:
- Use `.env.development` for local settings
- Mock external services when possible
- Enable debug logging for troubleshooting
- Use development-specific credentials

**Testing**:
- Isolated test configuration with `.env.test`
- Mock all external dependencies
- Use test-specific database instances
- Validate configuration in test setup

### Production

**Deployment**:
- Use environment variables for all secrets
- Validate configuration before deployment
- Monitor configuration health continuously
- Implement graceful degradation for missing config

**Security**:
- Rotate credentials regularly
- Use least-privilege access principles
- Monitor credential usage and access
- Implement secure credential storage

### Maintenance

**Configuration Updates**:
- Version control all configuration changes
- Test configuration changes in staging
- Implement rollback procedures
- Document configuration dependencies

**Monitoring**:
- Alert on configuration validation failures
- Monitor external service connectivity
- Track configuration change impact
- Maintain configuration audit logs
