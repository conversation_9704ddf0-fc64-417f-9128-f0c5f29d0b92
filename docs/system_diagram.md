# Zeitwahl AI Agent - System Diagram

## Architecture Overview

This file contains the minimal text diagram that describes the holistic system architecture. 
Modify this diagram when prompting the AI to reflect changes in system design.
When adding new features, design to the system add or modify all the necesarry components related to that feature if necessary:
- actors
- events
- services
- infrastructure components
- models
- context
- config
- constants
- ...

```
USER MESSAGE (First Time)
     ↓
┌─────────────────────────────────────────────────────────────────┐
│                    TELEGRAM BOT INTERFACE                      │
│                      (BotActor)                                │
│  • First Message Detection                                     │
│  • Timezone Selection Keyboard                                 │
│  • Callback Query Handling                                     │
│  • Post-Registration Flow                                      │
└─────────────────────────────────────────────────────────────────┘
     ↓ UserMessageReceived Event (with is_first_message flag)
┌─────────────────────────────────────────────────────────────────┐
│                      EVENT BUS                                 │
│              (Central Communication Hub)                       │
│  • FirstMessageReceived                                        │
│  • TimezoneSelectionRequired                                   │
│  • TimezoneSelected                                            │
│  • UserRegistrationCompleted                                   │
│  • NewUserRegistrationStarted                                  │
└─────────────────────────────────────────────────────────────────┘
     ↓ Event Distribution
┌─────────────────────────────────────────────────────────────────┐
│                   ACTOR SYSTEM                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │    DB       │  │Preprocessing│  │    LLM      │  │  Bot    │ │
│  │   Actor     │  │   Actor     │  │   Actor     │  │ Actor   │ │
│  │             │  │             │  │             │  │         │ │
│  │ • Users     │  │ • Validate  │  │ • API Calls │  │ • Send  │ │
│  │ • Messages  │  │ • Context   │  │ • Responses │  │ • Reply │ │
│  │ • Storage   │  │ • Prompts   │  │ • Tokens    │  │ • Handle│ │
│  │ • Timezone  │  │ • First Msg │  │ • Schedule  │  │ • Keybd │ │
│  │   Updates   │  │   Detection │  │   Intent    │  │ • Block │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
     ↓ Database Events        ↓ Context Events       ↓ LLM Events
┌─────────────────────────────────────────────────────────────────┐
│                    INFRASTRUCTURE LAYER                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │  MongoDB    │  │  Telegram   │  │   Google    │  │ Config  │ │
│  │  Database   │  │     API     │  │  Gemini API │  │ & Logs  │ │
│  │ • User TZ   │  │ • Inline KB │  │             │  │ • TZ    │ │
│  │ • Pending   │  │ • Callbacks │  │             │  │   Opts  │ │
│  │   Requests  │  │             │  │             │  │ • Sched │ │
│  │             │  │             │  │             │  │   Keys  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘

ENHANCED FLOW:
New User → First Message → Scheduling Detection → Registration → Timezone Selection →
Complete Registration → Reprocess Original Message (if scheduling) → Normal Flow
```

## Key Components

### Core Architecture
- **Event Bus**: Central communication hub for all actor interactions
- **Actor System**: Independent, asynchronous components with lifecycle management
- **Message Context**: End-to-end tracking with unique context_id for each message

### Actor Responsibilities
- **BotActor**: Telegram interface, message handling, user responses, timezone selection keyboard, callback query handling, first message detection, post-registration flow control
- **DBActor**: Database operations, user management, message storage, timezone updates, pending scheduling request management
- **PreprocessingActor**: Message validation, context building, prompt preparation, first message processing, scheduling keyword detection, timezone validation, context preservation
- **LLMCallsActor**: AI API calls, response generation, token management

### Event Flow Pattern
```
NEW USER FLOW:
UserMessage(first) → FirstMessage → Registration → TimezoneSelection → Complete → Reprocess
     ↓                    ↓              ↓              ↓               ↓          ↓
  BotActor → PreprocessActor → DBActor → BotActor → DBActor → PreprocessActor

EXISTING USER FLOW:
UserMessage → Validation → Context → LLM → Response → User
     ↓            ↓          ↓       ↓        ↓
  BotActor → PreprocessActor → LLMActor → BotActor
```

### Technology Stack
- **Python 3.13+** with AsyncIO for concurrency
- **MongoDB** for data persistence
- **Pydantic** for data validation and configuration
- **aiogram** for Telegram bot framework
- **Telegram Bot API** for user interface
- **Google Gemini API** for AI responses
- **Event-driven architecture** with actor isolation

## Modification Instructions

When modifying this diagram:
1. Keep the basic flow: User → Bot → EventBus → Actors → Services
2. Update actor responsibilities as needed
3. Maintain event-driven communication pattern
4. Preserve the layered architecture (Interface → Actors → Infrastructure)
5. Update technology stack as components change

## Feautures Examples

## Feature: Message Processing and User Interaction

```gherkin
Feature: Message Processing and User Interaction
  As an end user
  I want to send messages to the Zeitwahl bot
  So that I can interact with the AI scheduling assistant

  Background:
    Given the Zeitwahl bot is running and connected to Telegram
    And the database is initialized and accessible
    And the LLM service is available

  Scenario: First-time user sends a message
    Given I am a new user with Telegram ID "123456789"
    And I have username "john_doe" and first name "John"
    When I send the message "Hello, I need help with scheduling"
    Then the bot should create a new user profile for me
    And the bot should respond within 5 seconds
    And the response should acknowledge my message
    And my user profile should be stored in the database

  Scenario: Existing user sends a follow-up message
    Given I am an existing user with Telegram ID "123456789"
    And I have previous conversation history
    When I send the message "Can you schedule a meeting for tomorrow?"
    Then the bot should retrieve my conversation context
    And the bot should reference my previous interactions
    And the response should be contextually relevant

  Scenario: Message validation and safety
    Given I am an existing user
    When I send a message containing inappropriate content
    Then the bot should validate the message content
    And the bot should respond with a polite error message
    And the bot should provide guidance on appropriate usage

  Scenario: Rate limiting protection
    Given I am an existing user
    When I send 20 messages within 1 minute
    Then the bot should apply rate limiting after 10 messages
    And the bot should respond with a rate limit notification
    And subsequent messages should be queued or rejected
```


