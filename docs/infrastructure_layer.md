# Infrastructure Layer Documentation

## Overview

The infrastructure layer (`app/infrastructure/`) handles external integrations and provides the technical foundation for the Zeitwahl AI Agent. It includes database operations, Telegram bot integration, and LLM API management.

## Database Infrastructure (`app/infrastructure/database/`)

### Database Models

#### BotUser Model (`models/bot_user.py`)
Represents a Telegram user with comprehensive profile and activity tracking.

**Core Fields:**
- `telegram_user_id: int` - Unique Telegram user identifier (primary key)
- `username: Optional[str]` - Telegram username without @
- `first_name: Optional[str]` - User's first name
- `last_name: Optional[str]` - User's last name
- `language_code: Optional[str]` - User's language preference
- `timezone: str` - User's timezone (default: UTC)
- `is_active: bool` - User activity status
- `created_at: datetime` - Account creation timestamp
- `last_seen_at: Optional[datetime]` - Last activity timestamp
- `total_messages: int` - Message count for analytics

**Key Methods:**
- `update_last_seen()` - Update activity timestamp
- `increment_message_count()` - Increment message counter
- `get_display_name()` - Get formatted display name
- `to_dict() / from_dict()` - MongoDB serialization

#### ConversationMessage Model (`models/conversation_message.py`)
Represents messages in conversations with processing state tracking.

**Core Fields:**
- `telegram_message_id: int` - Unique Telegram message ID
- `chat_id: int` - Chat identifier
- `telegram_user_id: Optional[int]` - User ID (None for bot messages)
- `message_text: str` - Message content
- `message_type: str` - Message type (text, photo, document, etc.)
- `is_from_bot: bool` - Bot message indicator
- `telegram_timestamp: datetime` - Original Telegram timestamp
- `received_at: datetime` - Bot receipt timestamp
- `processing_status: str` - Processing state (received, processing, processed, error)
- `is_reply: bool` - Reply indicator
- `reply_to_message_id: Optional[int]` - Reply target message ID

**Key Methods:**
- `mark_as_processing()` - Update to processing state
- `mark_as_processed()` - Mark as completed with response
- `mark_as_error()` - Mark as failed with error details
- `get_conversation_key()` - Generate conversation identifier

#### UserChat Model (`models/user_chat.py`)
Tracks user participation in chats with activity metrics.

**Core Fields:**
- `telegram_user_id: int` - User identifier
- `chat_id: int` - Chat identifier
- `chat_type: str` - Chat type (private, group, supergroup, channel)
- `user_status: str` - User status in chat (member, admin, creator, left, kicked)
- `message_count: int` - Messages sent in this chat
- `first_message_at: datetime` - First message timestamp
- `last_message_at: datetime` - Last message timestamp

### Repository Layer

#### BotUserRepository (`repositories/bot_user_repository.py`)
Provides CRUD operations for BotUser entities.

**Core Methods:**
- `create_user(user: BotUser) -> BotUser` - Create new user
- `get_user_by_telegram_id(telegram_user_id: int) -> Optional[BotUser]` - Find user
- `update_user_last_seen(telegram_user_id: int) -> bool` - Update activity
- `increment_user_message_count(telegram_user_id: int) -> bool` - Increment counter
- `get_active_users(limit: int = 100) -> List[BotUser]` - Get active users
- `search_users(query: str) -> List[BotUser]` - Search users

#### ConversationMessageRepository (`repositories/conversation_message_repository.py`)
Manages conversation message storage and retrieval.

**Core Methods:**
- `create_message(message: ConversationMessage) -> ConversationMessage` - Store message
- `get_message_by_id(telegram_message_id: int, chat_id: int) -> Optional[ConversationMessage]` - Find message
- `get_conversation_history(chat_id: int, limit: int = 50) -> List[ConversationMessage]` - Get history
- `update_message_status(message_id: str, status: str) -> bool` - Update processing status
- `get_recent_messages(chat_id: int, hours: int = 24) -> List[ConversationMessage]` - Recent messages

### Database Services

#### DatabaseService (`services/database_service.py`)
High-level database operations with business logic.

**Core Methods:**
- `initialize_database()` - Set up database and indexes
- `health_check() -> Dict[str, Any]` - Database health status
- `get_user_stats() -> Dict[str, int]` - User statistics
- `get_message_stats() -> Dict[str, int]` - Message statistics
- `cleanup_old_data(days: int = 30)` - Data retention cleanup

#### Connection Manager (`connection_manager.py`)
Manages MongoDB connections with pooling and error handling.

**Features:**
- Connection pooling for performance
- Automatic reconnection on failures
- Health monitoring and metrics
- Transaction support for complex operations

### Database Actor (`db_actor.py`)

The DBActor handles all database operations through the event bus.

**Event Handlers:**
- `CreateUserRequest` → User creation with validation
- `GetUserByTelegramIdRequest` → User retrieval
- `UpdateUserLastSeenRequest` → Activity updates
- `CreateMessageRequest` → Message storage
- `GetConversationHistoryRequest` → History retrieval
- `InitializeDatabaseRequest` → Database setup
- `DatabaseHealthCheckRequest` → Health monitoring

**Key Features:**
- Event-driven database operations
- Automatic error handling and retry
- Transaction management
- Performance monitoring

## Telegram Integration (`app/infrastructure/telegram/`)

### Bot Actor (`bot_actor.py`)

The BotActor manages all Telegram bot interactions and message handling.

**Core Responsibilities:**
- Receive messages from Telegram API
- Create message contexts for tracking
- Publish UserMessageReceived events
- Send responses back to users
- Handle bot commands and interactions

**Event Handlers:**
- `SendMessageRequest` → Send message to user
- `SendTypingAction` → Show typing indicator
- `EditMessageRequest` → Edit existing message
- `DeleteMessageRequest` → Delete message

**Key Features:**
- Asynchronous message handling
- Context creation for each message
- Error handling and retry logic
- Rate limiting compliance
- Webhook and polling support

**Message Processing Flow:**
1. Receive message from Telegram
2. Create MessageContext with unique ID
3. Extract user and chat information
4. Publish UserMessageReceived event
5. Wait for response events
6. Send response back to Telegram

## LLM Integration (`app/infrastructure/llm/`)

### LLM Service (`services/llm_service.py`)

Manages multiple LLM providers with fallback support.

#### Provider Architecture

**Base LLMProvider Interface:**
- `generate_response(prompt: str, **kwargs) -> Dict[str, Any]` - Generate response
- `stream_response(prompt: str, **kwargs) -> AsyncIterator` - Streaming response
- `name: str` - Provider identifier

**Implemented Providers:**
- **GeminiProvider**: Google Gemini API integration
- **DeepseekProvider**: Deepseek API integration  
- **MockLLMProvider**: Testing and development provider

#### LLMService Features

**Multi-Provider Support:**
- Primary provider with fallback chain
- Automatic provider switching on failures
- Provider-specific configuration
- Load balancing capabilities

**Response Management:**
- Normalized response format across providers
- Token usage tracking and cost calculation
- Processing time monitoring
- Error handling and retry logic

**Core Methods:**
- `generate_response(prompt: str, **kwargs) -> Dict[str, Any]` - Generate with fallback
- `get_available_providers() -> List[str]` - List active providers
- `get_provider_status(provider: str) -> Dict[str, Any]` - Provider health
- `estimate_cost(prompt: str, provider: str) -> Dict[str, float]` - Cost estimation

### Token Calculator (`services/token_calculator.py`)

Provides token estimation and cost calculation for LLM operations.

**Core Features:**
- Token estimation for different content types
- Model-specific token limits
- Cost calculation per provider
- Usage analytics and reporting

**Key Methods:**
- `estimate_tokens(text: str) -> int` - Estimate token count
- `get_model_limit(model_name: str) -> int` - Get token limit
- `calculate_remaining_tokens(prompt: str, model: str) -> int` - Available tokens
- `estimate_cost(input_tokens: int, output_tokens: int, model: str) -> Dict` - Cost breakdown

**Token Estimation Logic:**
- Basic estimation: ~4 characters per token for English
- Code content: ~3 characters per token (more complex)
- Special characters: ~3.5 characters per token
- Adjustments based on content analysis

### LLM Calls Actor (`llm_calls_actor.py`)

The LLMCallsActor handles all LLM API interactions through the event bus.

**Event Handlers:**
- `LLMRequestReady` → Process LLM request with prompt
- `LLMHealthCheckRequest` → Check provider availability
- `LLMUsageStatsRequest` → Get usage statistics

**Core Responsibilities:**
- Interface with multiple LLM providers
- Handle API calls and response processing
- Manage token usage and cost tracking
- Provide fallback mechanisms for failures
- Monitor provider health and performance

**Processing Flow:**
1. Receive LLMRequestReady event
2. Select appropriate provider
3. Calculate token usage and cost
4. Make API call with error handling
5. Process and normalize response
6. Publish LLMResponseGenerated event
7. Update usage statistics

## Interface Definitions (`interfaces.py`)

### Core Interfaces

**IDatabaseConnection:**
- Database connection abstraction
- Transaction management
- Health monitoring

**IDatabaseService:**
- High-level database operations
- Business logic integration
- Event handling

**IUserRepository:**
- User data access operations
- CRUD interface for users

**IMessageRepository:**
- Message data access operations
- Conversation management

## Error Handling and Resilience

### Database Resilience
- Connection pooling with automatic reconnection
- Transaction rollback on failures
- Retry logic for transient errors
- Circuit breaker pattern for persistent failures

### Telegram Integration Resilience
- Rate limiting compliance
- Automatic retry with exponential backoff
- Webhook failure handling
- Message queue for reliable delivery

### LLM Integration Resilience
- Multi-provider fallback chain
- Timeout handling for slow responses
- Cost monitoring and budget limits
- Provider health monitoring

## Performance Optimization

### Database Performance
- Proper indexing for all query patterns
- Connection pooling for concurrent access
- Query optimization and caching
- Bulk operations for efficiency

### Telegram Performance
- Asynchronous message handling
- Batch operations where possible
- Efficient webhook processing
- Memory-efficient message storage

### LLM Performance
- Token usage optimization
- Response caching for repeated queries
- Streaming responses for long content
- Parallel provider calls for redundancy
