# Zeitwahl AI Agent - Documentation

## Overview

This directory contains comprehensive documentation for the Zeitwahl AI Agent, an AI-powered Telegram bot designed for intelligent calendar scheduling and meeting coordination.

## Documentation Structure

### 📋 Project Rules and Guidelines
- **[rules.md](rules.md)** - Coding conventions, development rules, and best practices

### 🏗️ Architecture Documentation
- **[architecture_overview.md](architecture_overview.md)** - System architecture, design patterns, and architectural decisions
- **[core_components.md](core_components.md)** - Core system components (Actor system, Event Bus, Message Context)
- **[infrastructure_layer.md](infrastructure_layer.md)** - Infrastructure components (Database, Telegram, LLM integrations)
- **[domain_layer.md](domain_layer.md)** - Business domain logic (Preprocessing, Scheduling services)
- **[configuration_management.md](configuration_management.md)** - Configuration structure and environment management

### 📖 Requirements and Features
- **[user_stories.md](user_stories.md)** - Comprehensive user stories reflecting all system features
- **[bdd_features.md](bdd_features.md)** - Cucumber-style BDD feature files with test scenarios

## Quick Navigation

### For Developers
Start with these documents to understand the system:
1. [Architecture Overview](architecture_overview.md) - Get the big picture
2. [Core Components](core_components.md) - Understand the foundation
3. [Infrastructure Layer](infrastructure_layer.md) - Learn about external integrations
4. [Domain Layer](domain_layer.md) - Understand business logic

### For Product Managers
Focus on these documents for feature understanding:
1. [User Stories](user_stories.md) - Complete feature catalog
2. [BDD Features](bdd_features.md) - Detailed behavior specifications
3. [Architecture Overview](architecture_overview.md) - Technical capabilities

### For System Administrators
These documents cover deployment and operations:
1. [Configuration Management](configuration_management.md) - Setup and configuration
2. [Architecture Overview](architecture_overview.md) - System requirements
3. [Infrastructure Layer](infrastructure_layer.md) - External dependencies

### For QA Engineers
Use these documents for testing:
1. [BDD Features](bdd_features.md) - Test scenarios and acceptance criteria
2. [User Stories](user_stories.md) - Feature requirements and acceptance criteria
3. [Core Components](core_components.md) - Component behavior and interfaces

## System Architecture Summary

The Zeitwahl AI Agent follows an **Actor-based architecture** with these key characteristics:

- **Event-Driven Communication**: All components communicate through a high-performance event bus
- **Message Context Tracking**: End-to-end tracking of user messages with unique context IDs
- **Microservice-Ready**: Actors can be deployed independently for horizontal scaling
- **Fault Tolerance**: Component failures are isolated and don't affect other parts of the system

### Core Actors
- **BotActor**: Telegram bot interface and message handling
- **DBActor**: Database operations and data persistence
- **PreprocessingActor**: Message validation and context building
- **LLMCallsActor**: AI/LLM integration and response generation

### Technology Stack
- **Python 3.13+** with AsyncIO for high concurrency
- **MongoDB** for flexible document storage
- **Telegram Bot API** for user interaction
- **Google Gemini & Deepseek APIs** for AI capabilities
- **Pydantic** for data validation and configuration

## Key Features

### 🤖 AI-Powered Scheduling
- Natural language understanding for scheduling requests
- Intelligent meeting coordination with multiple participants
- Context-aware responses based on conversation history
- Multi-provider LLM integration with automatic fallback

### 📅 Calendar Integration
- Google Calendar and Microsoft Outlook support
- Real-time availability checking and conflict detection
- Cross-timezone scheduling with automatic conversion
- Recurring event management and pattern recognition

### 🔧 System Reliability
- Actor-based architecture for fault isolation
- Event-driven communication for loose coupling
- Comprehensive error handling and graceful degradation
- Health monitoring and automatic recovery

### 🔒 Security & Privacy
- Encrypted data storage and transmission
- Secure credential management through environment variables
- Input validation and rate limiting
- Comprehensive audit logging

## Development Workflow

### Adding New Features
1. **Define User Stories**: Add to [user_stories.md](user_stories.md)
2. **Create BDD Scenarios**: Add to [bdd_features.md](bdd_features.md)
3. **Update Architecture**: Modify relevant architecture documents
4. **Implement**: Follow the Actor-based patterns
5. **Test**: Use BDD scenarios for acceptance testing

### Modifying Existing Components
1. **Review Architecture**: Understand component relationships
2. **Check Dependencies**: Review actor dependencies and event flows
3. **Update Documentation**: Keep architecture docs current
4. **Test Integration**: Verify event bus and actor interactions

### Configuration Changes
1. **Review Configuration Management**: Understand current structure
2. **Update Environment Variables**: Follow naming conventions
3. **Validate Settings**: Ensure proper validation is in place
4. **Update Documentation**: Keep configuration docs current

## Documentation Maintenance

### Keeping Documentation Current
- Update architecture docs when adding new components
- Add user stories for new features
- Create BDD scenarios for new functionality
- Review and update configuration documentation

### Documentation Standards
- Use clear, concise language
- Include code examples where helpful
- Maintain consistent formatting and structure
- Cross-reference related documents

### Review Process
- Architecture changes require documentation updates
- New features must include user stories and BDD scenarios
- Configuration changes must update relevant documentation
- Regular documentation reviews ensure accuracy

## Getting Help

### For Technical Questions
- Review [Core Components](core_components.md) for system internals
- Check [Infrastructure Layer](infrastructure_layer.md) for integration details
- Consult [Configuration Management](configuration_management.md) for setup issues

### For Feature Questions
- Review [User Stories](user_stories.md) for feature requirements
- Check [BDD Features](bdd_features.md) for detailed behavior
- Consult [Domain Layer](domain_layer.md) for business logic

### For Architecture Questions
- Start with [Architecture Overview](architecture_overview.md)
- Review specific component documentation as needed
- Check event flow diagrams and interaction patterns

## Contributing

When contributing to the Zeitwahl AI Agent:

1. **Follow Coding Rules**: Adhere to guidelines in [rules.md](rules.md)
2. **Update Documentation**: Keep all relevant docs current
3. **Add Tests**: Include BDD scenarios for new features
4. **Review Architecture**: Ensure changes align with system design

This documentation reflects the current state of the Zeitwahl AI Agent and should be updated as the system evolves.
