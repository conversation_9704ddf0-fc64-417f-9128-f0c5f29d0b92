# Zeitwahl AI Agent - Event Bus Events Reference

This document provides a comprehensive reference of all events that flow through the Zeitwahl AI Agent event bus system, including publishers, subscribers, and data structures.

## Event Categories

### 1. Core Message Processing Events

#### UserMessageReceived
**Publisher:** BotActor  
**Subscribers:** PreprocessingActor  
**Description:** Published when a user message is received from Telegram  
**Data Structure:**
```python
@dataclass
class UserMessageReceived(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    message_text: str
    message_type: str = "text"
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    telegram_timestamp: Optional[datetime] = None
    chat_type: str = "private"
    chat_title: Optional[str] = None
    is_reply: bool = False
    reply_to_message_id: Optional[int] = None
    is_first_message: bool = False  # NEW: First message detection
    timestamp: Optional[datetime] = None
```

#### UserIdentified
**Publisher:** DBActor  
**Subscribers:** PreprocessingActor  
**Description:** Published when user identification is completed  
**Data Structure:**
```python
@dataclass
class UserIdentified(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    user_exists: bool
    user_data: Optional[Dict[str, Any]] = None
    needs_registration: bool = False
    timestamp: Optional[datetime] = None
```

#### SendMessageRequest
**Publisher:** PreprocessingActor, LLMCallsActor, BotActor  
**Subscribers:** BotActor  
**Description:** Request to send a message to Telegram  
**Data Structure:**
```python
@dataclass
class SendMessageRequest(BaseEvent):
    chat_id: int
    message_text: str
    reply_to_message_id: Optional[int] = None
    parse_mode: Optional[str] = None
    context_id: Optional[str] = None
    timestamp: Optional[datetime] = None
```

#### MessageSent
**Publisher:** BotActor  
**Subscribers:** DBActor  
**Description:** Confirmation that a message was sent  
**Data Structure:**
```python
@dataclass
class MessageSent(BaseEvent):
    chat_id: int
    telegram_message_id: int
    message_text: str
    success: bool
    error_message: Optional[str] = None
    context_id: Optional[str] = None
    timestamp: Optional[datetime] = None
```

### 2. NEW: First Message Processing Events

#### FirstMessageReceived
**Publisher:** PreprocessingActor  
**Subscribers:** None (informational)  
**Description:** Published when a new user's first message is detected  
**Data Structure:**
```python
@dataclass
class FirstMessageReceived(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    message_text: str
    has_scheduling_keywords: bool = False
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    timestamp: Optional[datetime] = None
```

#### NewUserRegistrationStarted
**Publisher:** PreprocessingActor  
**Subscribers:** None (informational)  
**Description:** Published when new user registration process begins  
**Data Structure:**
```python
@dataclass
class NewUserRegistrationStarted(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    user_info: Dict[str, Any]
    pending_scheduling_request: bool = False
    timestamp: Optional[datetime] = None
```

### 3. NEW: Timezone Selection Events

#### TimezoneSelectionRequired
**Publisher:** DBActor  
**Subscribers:** BotActor  
**Description:** Published when user needs to select timezone  
**Data Structure:**
```python
@dataclass
class TimezoneSelectionRequired(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    user_id: str
    pending_scheduling_request: bool = False
    timestamp: Optional[datetime] = None
```

#### TimezoneSelected
**Publisher:** BotActor  
**Subscribers:** PreprocessingActor  
**Description:** Published when user selects a timezone  
**Data Structure:**
```python
@dataclass
class TimezoneSelected(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    user_id: str
    selected_timezone: str
    callback_query_id: Optional[str] = None
    timestamp: Optional[datetime] = None
```

#### UserRegistrationCompleted
**Publisher:** PreprocessingActor  
**Subscribers:** BotActor  
**Description:** Published when user registration is fully completed  
**Data Structure:**
```python
@dataclass
class UserRegistrationCompleted(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    user_id: str
    timezone: str
    pending_scheduling_request: bool = False
    original_message: Optional[str] = None
    timestamp: Optional[datetime] = None
```

### 4. Database Events

#### user_identification_request
**Publisher:** PreprocessingActor  
**Subscribers:** DBActor  
**Description:** Request to identify a user  
**Data Structure:**
```python
{
    "context_id": str,
    "telegram_user_id": int,
    "chat_id": int,
    "telegram_message_id": int,
    "chat_type": str,
    "chat_title": Optional[str],
    "user_info": {
        "username": Optional[str],
        "first_name": Optional[str],
        "last_name": Optional[str]
    },
    "timestamp": datetime
}
```

#### user_registration_request
**Publisher:** PreprocessingActor  
**Subscribers:** DBActor  
**Description:** Request to register a new user  
**Data Structure:**
```python
{
    "context_id": str,
    "telegram_user_id": int,
    "chat_id": int,
    "telegram_message_id": int,
    "user_info": {
        "username": Optional[str],
        "first_name": Optional[str],
        "last_name": Optional[str],
        "language_code": Optional[str],
        "is_bot": bool
    },
    "pending_scheduling_request": bool  # NEW
}
```

#### user_timezone_update_request (NEW)
**Publisher:** PreprocessingActor  
**Subscribers:** DBActor  
**Description:** Request to update user's timezone  
**Data Structure:**
```python
{
    "user_id": str,
    "timezone": str,
    "context_id": str
}
```

#### store_message_request
**Publisher:** PreprocessingActor  
**Subscribers:** DBActor  
**Description:** Request to store a message  
**Data Structure:**
```python
{
    "context_id": str,
    "telegram_message_id": int,
    "chat_id": int,
    "telegram_user_id": int,
    "message_text": str,
    "message_type": str,
    "is_from_bot": bool,
    "telegram_timestamp": datetime,
    "is_reply": bool,
    "reply_to_message_id": Optional[int],
    "chat_type": str,
    "chat_title": Optional[str]
}
```

### 5. LLM Processing Events

#### ContextBuilt
**Publisher:** PreprocessingActor  
**Subscribers:** LLMCallsActor  
**Description:** Published when message context is built  
**Data Structure:**
```python
@dataclass
class ContextBuilt(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    context_data: Dict[str, Any]
    timestamp: Optional[datetime] = None
```

#### LLMRequestReady
**Publisher:** PreprocessingActor  
**Subscribers:** LLMCallsActor  
**Description:** Published when LLM request is ready  
**Data Structure:**
```python
@dataclass
class LLMRequestReady(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    prompt: str
    model_config: Dict[str, Any]
    timestamp: Optional[datetime] = None
```

#### LLMResponseGenerated
**Publisher:** LLMCallsActor  
**Subscribers:** BotActor  
**Description:** Published when LLM generates a response  
**Data Structure:**
```python
@dataclass
class LLMResponseGenerated(BaseEvent):
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    response_text: str
    model_used: str
    tokens_used: int
    processing_time: float
    timestamp: Optional[datetime] = None
```

### 6. Generic Response Events

#### GenericResponse
**Publisher:** PreprocessingActor  
**Subscribers:** BotActor  
**Description:** Request for a generic response  
**Data Structure:**
```python
@dataclass
class GenericResponse(BaseEvent):
    chat_id: int
    message_type: str  # "new_user", "irrelevant_message", "error"
    reply_to_message_id: Optional[int] = None
    timestamp: Optional[datetime] = None
```

### 7. Error Events

#### ErrorOccurred
**Publisher:** All Actors
**Subscribers:** ErrorHandler
**Description:** Published when an error occurs
**Data Structure:**
```python
@dataclass
class ErrorOccurred(BaseEvent):
    actor_name: str
    error_type: str
    error_message: str
    context_data: Dict[str, Any]
    timestamp: Optional[datetime] = None
```

## Event Flow Diagrams

### New User Registration Flow
```
1. BotActor receives first message
   ↓ UserMessageReceived(is_first_message=True)
2. PreprocessingActor detects first message + scheduling intent
   ↓ FirstMessageReceived(has_scheduling_keywords=True)
   ↓ NewUserRegistrationStarted(pending_scheduling_request=True)
3. DBActor creates user (timezone=None)
   ↓ TimezoneSelectionRequired
4. BotActor shows timezone keyboard
   ↓ User selects timezone
   ↓ TimezoneSelected
5. PreprocessingActor validates timezone
   ↓ UserRegistrationCompleted(pending_scheduling_request=True)
6. BotActor reprocesses original scheduling message
```

### Existing User Message Flow
```
1. BotActor receives message
   ↓ UserMessageReceived(is_first_message=False)
2. PreprocessingActor validates message
   ↓ ContextBuilt
3. PreprocessingActor builds prompt
   ↓ LLMRequestReady
4. LLMCallsActor processes request
   ↓ LLMResponseGenerated
5. BotActor sends response
   ↓ SendMessageRequest
   ↓ MessageSent
```

### Timezone Selection Flow
```
1. DBActor creates user without timezone
   ↓ TimezoneSelectionRequired
2. BotActor creates inline keyboard
   ↓ User clicks timezone button
3. BotActor handles callback query
   ↓ TimezoneSelected
4. PreprocessingActor validates timezone
   ↓ DBActor updates user timezone
   ↓ UserRegistrationCompleted
5. BotActor handles post-registration flow
```

## Actor Event Subscriptions Summary

### BotActor
**Subscribes to:**
- send_message_request
- generic_response
- timezone_selection_required (NEW)
- user_registration_completed (NEW)

**Publishes:**
- user_message_received
- message_sent
- timezone_selected (NEW)

### PreprocessingActor
**Subscribes to:**
- user_message_received
- user_identified
- timezone_selected (NEW)

**Publishes:**
- first_message_received (NEW)
- new_user_registration_started (NEW)
- user_identification_request
- user_registration_request
- user_timezone_update_request (NEW)
- store_message_request
- context_built
- llm_request_ready
- generic_response
- error_occurred

### DBActor
**Subscribes to:**
- user_identification_request
- user_registration_request
- user_timezone_update_request (NEW)
- store_message_request

**Publishes:**
- user_identified
- user_registered
- timezone_selection_required (NEW)
- user_registration_completed (NEW)
- message_stored
- error_occurred

### LLMCallsActor
**Subscribes to:**
- llm_request_ready

**Publishes:**
- llm_response_generated
- error_occurred

## Enhanced Data Models

### MessageContext (Enhanced)
```python
@dataclass
class MessageContext:
    # ... existing fields ...
    pending_scheduling_request: bool = False  # NEW
```

### BotUser (Enhanced)
```python
class BotUser(BaseModel):
    # ... existing fields ...
    timezone: Optional[str] = Field(default=None)  # CHANGED: Now nullable
```

## Constants and Configuration

### Timezone Constants
- **File:** `app/core/constants/timezone_constants.py`
- **TIMEZONE_OPTIONS:** List of UTC-12 to UTC+14 options
- **Functions:** `validate_timezone()`, `get_timezone_keyboard_layout()`

### Scheduling Constants
- **File:** `app/core/constants/scheduling_constants.py`
- **SCHEDULING_KEYWORDS:** Primary scheduling keywords
- **TIME_KEYWORDS:** Time-related keywords
- **Functions:** `has_scheduling_keywords()`, `get_scheduling_confidence_score()`

## Testing Coverage

### Test Files
- `tests/test_first_message_flow.py` - Comprehensive tests for new functionality
- Tests cover:
  - Scheduling keyword detection
  - Timezone validation
  - First message processing
  - Timezone selection flow
  - Post-registration flow

## Migration Notes

### Breaking Changes
1. `BotUser.timezone` is now nullable (was default "UTC")
2. `UserMessageReceived` has new `is_first_message` field
3. `MessageContext` has new `pending_scheduling_request` field

### New Dependencies
1. New constants modules for timezone and scheduling
2. Enhanced event types for registration flow
3. Additional database methods for timezone updates

### Deployment Considerations
1. Database migration may be needed for existing users with timezone="UTC"
2. New event handlers need to be registered
3. Inline keyboard functionality requires aiogram callback query handling
