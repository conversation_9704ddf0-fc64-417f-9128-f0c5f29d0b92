# Domain Layer Documentation

## Overview

The domain layer (`app/domain/`) contains the business logic and domain-specific functionality of the Zeitwahl AI Agent. It implements the core scheduling and meeting coordination features through specialized services and actors.

## Preprocessing Pipeline (`app/domain/preprocessing/`)

The preprocessing pipeline validates, enriches, and prepares user messages for LLM processing.

### Preprocessing Actor (`preprocessing_actor.py`)

The PreprocessingActor orchestrates the entire message preprocessing workflow.

**Core Responsibilities:**
- Coordinate preprocessing services
- Validate user messages and context
- Build conversation context
- Prepare prompts for LLM processing
- Handle preprocessing errors and edge cases

**Event Handlers:**
- `UserMessageReceived` → Start preprocessing workflow
- `UserIdentificationRequest` → Validate and identify users
- `ContextBuildingRequest` → Build conversation context
- `PromptPreparationRequest` → Prepare LLM prompts

**Processing Workflow:**
1. **User Identification**: Validate user and create/update user record
2. **Context Building**: Gather conversation history and context
3. **Message Validation**: Check message content and format
4. **Prompt Preparation**: Build structured prompt for LLM
5. **Event Publishing**: Publish LLMRequestReady event

**Key Features:**
- Asynchronous processing pipeline
- Error handling with graceful degradation
- Context-aware processing
- Performance monitoring and metrics

### Validation Service (`services/validation_service.py`)

Provides comprehensive message validation and filtering capabilities.

**Core Responsibilities:**
- Pre-filter messages before LLM processing
- Validate message content and format
- Check for spam, abuse, or inappropriate content
- Apply business rules and constraints
- Rate limiting and usage validation

**Validation Categories:**

**Content Validation:**
- Message length limits (min/max)
- Character encoding validation
- Language detection and filtering
- Profanity and content filtering
- Spam detection algorithms

**Business Rule Validation:**
- User permission checks
- Feature availability validation
- Rate limiting enforcement
- Usage quota validation
- Time-based restrictions

**Security Validation:**
- Input sanitization
- Injection attack prevention
- Malicious content detection
- User authentication validation

**Key Methods:**
- `validate_message_content(message: str) -> ValidationResult` - Content validation
- `validate_user_permissions(user_id: int, action: str) -> bool` - Permission check
- `check_rate_limits(user_id: int) -> RateLimitResult` - Rate limiting
- `sanitize_input(content: str) -> str` - Input sanitization
- `detect_spam(message: str, user_history: List) -> bool` - Spam detection

**Validation Result Structure:**
```python
@dataclass
class ValidationResult:
    is_valid: bool
    error_code: Optional[str]
    error_message: Optional[str]
    suggestions: List[str]
    severity: str  # info, warning, error, critical
```

### Context Grabber Service (`services/context_grabber_service.py`)

Builds comprehensive conversation context for LLM processing.

**Core Responsibilities:**
- Retrieve conversation history
- Extract relevant context information
- Build user profile context
- Gather scheduling-related context
- Optimize context for token efficiency

**Context Building Process:**

**1. Conversation History:**
- Retrieve recent messages from conversation
- Filter relevant messages based on recency and importance
- Maintain conversation threading and reply context
- Optimize message selection for token limits

**2. User Context:**
- User profile information and preferences
- Previous scheduling interactions
- User timezone and availability patterns
- Communication style and preferences

**3. Scheduling Context:**
- Current calendar availability
- Upcoming meetings and conflicts
- Recurring event patterns
- Meeting preferences and constraints

**4. System Context:**
- Current date and time
- System capabilities and limitations
- Available calendar integrations
- Feature availability for user

**Key Methods:**
- `build_conversation_context(chat_id: int, limit: int) -> ConversationContext` - Build context
- `get_user_context(user_id: int) -> UserContext` - User information
- `get_scheduling_context(user_id: int) -> SchedulingContext` - Calendar context
- `optimize_context_for_tokens(context: Context, limit: int) -> Context` - Token optimization

**Context Optimization:**
- Prioritize recent and relevant messages
- Summarize older conversation history
- Remove redundant or irrelevant information
- Balance context completeness with token efficiency

### Prompt Builder Service (`services/prompt_builder_service.py`)

Constructs structured prompts for LLM processing with context and instructions.

**Core Responsibilities:**
- Build structured prompts from context
- Include system instructions and capabilities
- Format conversation history appropriately
- Add scheduling-specific instructions
- Optimize prompt structure for different LLM providers

**Prompt Structure:**

**1. System Instructions:**
- Bot identity and capabilities
- Scheduling domain expertise
- Response format guidelines
- Constraint and limitation information

**2. Context Section:**
- User information and preferences
- Conversation history summary
- Current scheduling context
- Available calendar integrations

**3. User Message:**
- Current user message
- Message metadata and context
- Reply context if applicable

**4. Task Instructions:**
- Specific task guidance
- Expected response format
- Tool usage instructions
- Error handling guidelines

**Key Methods:**
- `build_scheduling_prompt(context: Context, message: str) -> str` - Main prompt builder
- `format_conversation_history(messages: List) -> str` - History formatting
- `add_system_instructions(prompt: str) -> str` - System instruction injection
- `optimize_prompt_for_provider(prompt: str, provider: str) -> str` - Provider optimization

**Prompt Templates:**
- **Scheduling Request**: For meeting scheduling tasks
- **Calendar Query**: For availability and calendar information
- **General Chat**: For non-scheduling conversations
- **Error Recovery**: For handling errors and clarifications

## Scheduling Domain (`app/domain/scheduling/`)

The scheduling domain implements calendar integration and meeting coordination logic.

### Calendar Service (`services/calendar_service.py`)

Provides calendar integration and meeting management capabilities.

**Core Responsibilities:**
- Integrate with multiple calendar providers
- Manage calendar events and availability
- Handle meeting scheduling and coordination
- Resolve scheduling conflicts
- Support recurring events and patterns

**Calendar Provider Support:**
- **Google Calendar**: Primary calendar integration
- **Microsoft Outlook**: Enterprise calendar support
- **Apple Calendar**: Future integration planned
- **Generic CalDAV**: Standard calendar protocol support

**Core Methods:**
- `get_availability(user_id: int, start: datetime, end: datetime) -> List[TimeSlot]` - Check availability
- `create_event(user_id: int, event: CalendarEvent) -> CalendarEvent` - Create meeting
- `update_event(user_id: int, event_id: str, updates: Dict) -> CalendarEvent` - Update meeting
- `delete_event(user_id: int, event_id: str) -> bool` - Cancel meeting
- `find_meeting_slots(participants: List[int], duration: int, constraints: Dict) -> List[TimeSlot]` - Find slots
- `resolve_conflicts(user_id: int, new_event: CalendarEvent) -> ConflictResolution` - Handle conflicts

**Calendar Event Structure:**
```python
@dataclass
class CalendarEvent:
    title: str
    description: Optional[str]
    start_time: datetime
    end_time: datetime
    participants: List[str]
    location: Optional[str]
    meeting_type: str  # in-person, video, phone
    recurrence_rule: Optional[str]
    reminders: List[Reminder]
    metadata: Dict[str, Any]
```

**Availability Management:**
- Real-time availability checking
- Conflict detection and resolution
- Time zone handling and conversion
- Working hours and preferences
- Recurring availability patterns

### User Service (`services/user_service.py`)

Manages user-specific scheduling preferences and coordination.

**Core Responsibilities:**
- Manage user scheduling preferences
- Handle user availability patterns
- Coordinate multi-user scheduling
- Manage user calendar integrations
- Track scheduling history and patterns

**User Preference Management:**
- Default meeting duration preferences
- Preferred meeting times and days
- Time zone and location settings
- Meeting type preferences (video, in-person, phone)
- Notification and reminder preferences

**Multi-User Coordination:**
- Find common availability across participants
- Handle scheduling conflicts and negotiations
- Manage meeting invitations and responses
- Coordinate time zone differences
- Handle participant availability changes

**Key Methods:**
- `get_user_preferences(user_id: int) -> UserPreferences` - Get preferences
- `update_user_preferences(user_id: int, preferences: Dict) -> bool` - Update preferences
- `find_common_availability(user_ids: List[int], constraints: Dict) -> List[TimeSlot]` - Multi-user availability
- `coordinate_meeting(organizer_id: int, participants: List[int], requirements: Dict) -> MeetingProposal` - Meeting coordination
- `handle_scheduling_conflict(user_id: int, conflict: Conflict) -> Resolution` - Conflict resolution

## Postprocessing Pipeline (`app/domain/postprocessing/`)

*Future implementation planned*

The postprocessing pipeline will handle response formatting, enhancement, and delivery optimization.

**Planned Features:**
- Response formatting and styling
- Multi-language support and translation
- Response personalization based on user preferences
- Content enhancement and enrichment
- Delivery optimization and scheduling

## Business Logic Integration

### Event-Driven Processing

All domain services integrate with the event bus for loose coupling:

**Event Publishing:**
- Domain services publish events for state changes
- Processing milestones trigger appropriate events
- Error conditions generate error events
- Completion events signal successful processing

**Event Subscription:**
- Services subscribe to relevant domain events
- Cross-service coordination through events
- Asynchronous processing workflows
- Event-driven state management

### Error Handling and Recovery

**Graceful Degradation:**
- Services continue operating with reduced functionality
- Fallback mechanisms for external service failures
- User-friendly error messages and suggestions
- Automatic retry with exponential backoff

**Error Categories:**
- **Validation Errors**: User input issues with suggestions
- **Service Errors**: External service failures with fallbacks
- **Business Logic Errors**: Domain rule violations with explanations
- **System Errors**: Technical issues with recovery procedures

### Performance Optimization

**Caching Strategies:**
- User preference caching for fast access
- Calendar availability caching with TTL
- Conversation context caching
- Prompt template caching

**Asynchronous Processing:**
- Non-blocking service operations
- Parallel processing where possible
- Background task scheduling
- Resource pooling and management

**Resource Management:**
- Token usage optimization for LLM calls
- API rate limiting compliance
- Memory efficient context building
- Database query optimization

## Integration Patterns

### Service Composition

Domain services are composed to create complex workflows:

1. **Validation** → **Context Building** → **Prompt Preparation**
2. **Calendar Integration** → **Availability Checking** → **Meeting Scheduling**
3. **User Management** → **Preference Application** → **Personalized Responses**

### Cross-Domain Communication

Services communicate across domain boundaries through events:

- **Preprocessing** → **Scheduling**: Context and user information
- **Scheduling** → **Infrastructure**: Calendar API calls and data persistence
- **Domain** → **Core**: Event publishing and context updates

### External Service Integration

Domain services integrate with external APIs and services:

- **Calendar APIs**: Google Calendar, Outlook, CalDAV
- **Time Zone Services**: World time zone data and conversion
- **Natural Language Processing**: Enhanced text analysis
- **Notification Services**: Email, SMS, push notifications
