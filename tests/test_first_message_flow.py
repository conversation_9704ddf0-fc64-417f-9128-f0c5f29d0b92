"""
Tests for first message processing and timezone selection flow.

This module tests the enhanced functionality for new user registration,
timezone selection, and scheduling intent detection.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from app.core.events.events import (
    UserMessageReceived, FirstMessageReceived, NewUserRegistrationStarted,
    TimezoneSelectionRequired, TimezoneSelected, UserRegistrationCompleted
)
from app.core.constants.scheduling_constants import has_scheduling_keywords, get_scheduling_confidence_score
from app.core.constants.timezone_constants import validate_timezone, get_timezone_keyboard_layout
from app.core.context.message_context import MessageContext, MessageState
from app.domain.preprocessing.preprocessing_actor import PreprocessingActor
from app.infrastructure.telegram.bot_actor import BotActor
from app.infrastructure.database.db_actor import DBActor


class TestSchedulingKeywordDetection:
    """Test scheduling keyword detection functionality."""
    
    def test_has_scheduling_keywords_positive(self):
        """Test detection of scheduling keywords in messages."""
        test_cases = [
            "I need to schedule a meeting",
            "Can you help me book an appointment?",
            "Let's plan a call for tomorrow",
            "I want to organize a conference",
            "Set up a reminder for next week",
            "Schedule something for Monday morning"
        ]
        
        for message in test_cases:
            assert has_scheduling_keywords(message), f"Should detect scheduling in: {message}"
    
    def test_has_scheduling_keywords_negative(self):
        """Test non-scheduling messages are not detected."""
        test_cases = [
            "Hello, how are you?",
            "What's the weather like?",
            "I love pizza",
            "Tell me a joke",
            "How do I cook pasta?"
        ]
        
        for message in test_cases:
            assert not has_scheduling_keywords(message), f"Should not detect scheduling in: {message}"
    
    def test_scheduling_confidence_score(self):
        """Test confidence scoring for scheduling intent."""
        high_confidence_cases = [
            ("I need to schedule a meeting for tomorrow at 3pm", 0.7),
            ("Can you book an appointment with the doctor?", 0.5),
            ("Let's plan a conference call next week", 0.6)
        ]
        
        for message, min_score in high_confidence_cases:
            score = get_scheduling_confidence_score(message)
            assert score >= min_score, f"Score {score} should be >= {min_score} for: {message}"
        
        low_confidence_cases = [
            ("Hello there", 0.1),
            ("What's the weather?", 0.1)
        ]
        
        for message, max_score in low_confidence_cases:
            score = get_scheduling_confidence_score(message)
            assert score <= max_score, f"Score {score} should be <= {max_score} for: {message}"


class TestTimezoneConstants:
    """Test timezone constants and validation."""
    
    def test_validate_timezone(self):
        """Test timezone validation."""
        valid_timezones = ["UTC+0", "UTC-5", "UTC+8", "UTC-12", "UTC+14"]
        for tz in valid_timezones:
            assert validate_timezone(tz), f"Should validate timezone: {tz}"
        
        invalid_timezones = ["UTC+15", "UTC-13", "INVALID", ""]
        for tz in invalid_timezones:
            assert not validate_timezone(tz), f"Should not validate timezone: {tz}"
    
    def test_timezone_keyboard_layout(self):
        """Test timezone keyboard layout generation."""
        layout = get_timezone_keyboard_layout()
        
        # Should have multiple rows
        assert len(layout) > 0, "Layout should have rows"
        
        # Each row should have timezone options
        for row in layout:
            assert len(row) > 0, "Each row should have timezone options"
            for tz_option in row:
                assert "label" in tz_option, "Each option should have a label"
                assert "value" in tz_option, "Each option should have a value"
                assert "offset" in tz_option, "Each option should have an offset"


class TestFirstMessageProcessing:
    """Test first message processing in PreprocessingActor."""
    
    @pytest.fixture
    def preprocessing_actor(self):
        """Create a PreprocessingActor for testing."""
        return PreprocessingActor()
    
    @pytest.fixture
    def mock_event_bus(self):
        """Mock event bus for testing."""
        with patch('app.domain.preprocessing.preprocessing_actor.event_bus') as mock:
            mock.publish = AsyncMock()
            mock.send = AsyncMock()
            yield mock
    
    @pytest.fixture
    def mock_context_manager(self):
        """Mock message context manager."""
        with patch('app.domain.preprocessing.preprocessing_actor.message_context_manager') as mock:
            context = MessageContext(
                telegram_message_id=123,
                chat_id=456,
                message_text="Test message",
                telegram_user_id=789
            )
            mock.get_context.return_value = context
            yield mock, context
    
    @pytest.mark.asyncio
    async def test_first_message_with_scheduling_intent(self, preprocessing_actor, mock_event_bus, mock_context_manager):
        """Test first message processing with scheduling keywords."""
        mock_manager, context = mock_context_manager
        
        # Create first message event with scheduling content
        event_data = UserMessageReceived(
            context_id="test-context",
            telegram_user_id=789,
            chat_id=456,
            telegram_message_id=123,
            message_text="I need to schedule a meeting for tomorrow",
            is_first_message=True
        )
        
        # Process the message
        await preprocessing_actor.handle_user_message(event_data)
        
        # Verify scheduling intent was detected and stored
        assert context.pending_scheduling_request, "Should mark pending scheduling request"
        
        # Verify FirstMessageReceived event was published
        mock_event_bus.publish.assert_any_call("first_message_received", pytest.any(FirstMessageReceived))
    
    @pytest.mark.asyncio
    async def test_first_message_without_scheduling_intent(self, preprocessing_actor, mock_event_bus, mock_context_manager):
        """Test first message processing without scheduling keywords."""
        mock_manager, context = mock_context_manager
        
        # Create first message event without scheduling content
        event_data = UserMessageReceived(
            context_id="test-context",
            telegram_user_id=789,
            chat_id=456,
            telegram_message_id=123,
            message_text="Hello, how are you?",
            is_first_message=True
        )
        
        # Process the message
        await preprocessing_actor.handle_user_message(event_data)
        
        # Verify no scheduling intent was detected
        assert not context.pending_scheduling_request, "Should not mark pending scheduling request"
        
        # Verify FirstMessageReceived event was still published
        mock_event_bus.publish.assert_any_call("first_message_received", pytest.any(FirstMessageReceived))


class TestTimezoneSelection:
    """Test timezone selection flow in BotActor."""
    
    @pytest.fixture
    def bot_actor(self):
        """Create a BotActor for testing."""
        actor = BotActor()
        actor.bot = MagicMock()
        return actor
    
    @pytest.mark.asyncio
    async def test_timezone_selection_keyboard_creation(self, bot_actor):
        """Test creation of timezone selection keyboard."""
        keyboard = bot_actor._create_timezone_keyboard()
        
        # Should have inline keyboard structure
        assert hasattr(keyboard, 'inline_keyboard'), "Should have inline_keyboard attribute"
        assert len(keyboard.inline_keyboard) > 0, "Should have keyboard rows"
        
        # Check button structure
        for row in keyboard.inline_keyboard:
            for button in row:
                assert hasattr(button, 'text'), "Button should have text"
                assert hasattr(button, 'callback_data'), "Button should have callback_data"
                assert button.callback_data.startswith('timezone_'), "Callback data should start with timezone_"
    
    @pytest.mark.asyncio
    async def test_timezone_selection_handling(self, bot_actor):
        """Test handling of timezone selection callback."""
        # Mock callback query
        callback_query = MagicMock()
        callback_query.data = "timezone_UTC+3"
        callback_query.from_user.id = 789
        callback_query.message.chat.id = 456
        callback_query.message.message_id = 123
        callback_query.answer = AsyncMock()
        callback_query.message.edit_text = AsyncMock()
        
        with patch('app.infrastructure.telegram.bot_actor.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            with patch('app.infrastructure.telegram.bot_actor.message_context_manager') as mock_context_manager:
                context = MessageContext(
                    telegram_message_id=123,
                    chat_id=456,
                    message_text="Selected timezone: UTC+3",
                    telegram_user_id=789
                )
                mock_context_manager.create_context.return_value = context
                
                # Handle timezone selection
                await bot_actor._handle_timezone_selection(callback_query)
                
                # Verify callback was answered
                callback_query.answer.assert_called_once()
                
                # Verify TimezoneSelected event was published
                mock_event_bus.publish.assert_called_with("timezone_selected", pytest.any(TimezoneSelected))
                
                # Verify message was updated
                callback_query.message.edit_text.assert_called_once()


class TestPostRegistrationFlow:
    """Test post-registration flow handling."""
    
    @pytest.mark.asyncio
    async def test_registration_completed_with_pending_scheduling(self):
        """Test handling of completed registration with pending scheduling request."""
        bot_actor = BotActor()
        bot_actor.bot = MagicMock()
        
        with patch('app.infrastructure.telegram.bot_actor.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Create registration completed event with pending scheduling
            event_data = UserRegistrationCompleted(
                context_id="test-context",
                telegram_user_id=789,
                chat_id=456,
                user_id="user123",
                timezone="UTC+3",
                pending_scheduling_request=True,
                original_message="I need to schedule a meeting"
            )
            
            # Handle registration completion
            await bot_actor.handle_user_registration_completed(event_data)
            
            # Verify appropriate message was sent
            mock_event_bus.publish.assert_called_once()
            call_args = mock_event_bus.publish.call_args
            assert call_args[0][0] == "send_message_request"
            assert "scheduling request" in call_args[0][1].message_text
    
    @pytest.mark.asyncio
    async def test_registration_completed_without_pending_scheduling(self):
        """Test handling of completed registration without pending scheduling request."""
        bot_actor = BotActor()
        bot_actor.bot = MagicMock()
        
        with patch('app.infrastructure.telegram.bot_actor.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Create registration completed event without pending scheduling
            event_data = UserRegistrationCompleted(
                context_id="test-context",
                telegram_user_id=789,
                chat_id=456,
                user_id="user123",
                timezone="UTC+3",
                pending_scheduling_request=False,
                original_message=None
            )
            
            # Handle registration completion
            await bot_actor.handle_user_registration_completed(event_data)
            
            # Verify standard welcome message was sent
            mock_event_bus.publish.assert_called_once()
            call_args = mock_event_bus.publish.call_args
            assert call_args[0][0] == "send_message_request"
            assert "How can I help you" in call_args[0][1].message_text
