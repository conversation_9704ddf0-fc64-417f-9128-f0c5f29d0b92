"""
Timezone constants for the Zeitwahl AI Agent system.

This module contains timezone options and related constants for
the timezone selection flow during user registration.
"""

from typing import List, Dict, Any

# Timezone options from UTC-12 to UTC+14
TIMEZONE_OPTIONS: List[Dict[str, Any]] = [
    {"offset": -12, "label": "UTC-12", "value": "UTC-12"},
    {"offset": -11, "label": "UTC-11", "value": "UTC-11"},
    {"offset": -10, "label": "UTC-10", "value": "UTC-10"},
    {"offset": -9, "label": "UTC-9", "value": "UTC-9"},
    {"offset": -8, "label": "UTC-8", "value": "UTC-8"},
    {"offset": -7, "label": "UTC-7", "value": "UTC-7"},
    {"offset": -6, "label": "UTC-6", "value": "UTC-6"},
    {"offset": -5, "label": "UTC-5", "value": "UTC-5"},
    {"offset": -4, "label": "UTC-4", "value": "UTC-4"},
    {"offset": -3, "label": "UTC-3", "value": "UTC-3"},
    {"offset": -2, "label": "UTC-2", "value": "UTC-2"},
    {"offset": -1, "label": "UTC-1", "value": "UTC-1"},
    {"offset": 0, "label": "UTC+0", "value": "UTC+0"},
    {"offset": 1, "label": "UTC+1", "value": "UTC+1"},
    {"offset": 2, "label": "UTC+2", "value": "UTC+2"},
    {"offset": 3, "label": "UTC+3", "value": "UTC+3"},
    {"offset": 4, "label": "UTC+4", "value": "UTC+4"},
    {"offset": 5, "label": "UTC+5", "value": "UTC+5"},
    {"offset": 6, "label": "UTC+6", "value": "UTC+6"},
    {"offset": 7, "label": "UTC+7", "value": "UTC+7"},
    {"offset": 8, "label": "UTC+8", "value": "UTC+8"},
    {"offset": 9, "label": "UTC+9", "value": "UTC+9"},
    {"offset": 10, "label": "UTC+10", "value": "UTC+10"},
    {"offset": 11, "label": "UTC+11", "value": "UTC+11"},
    {"offset": 12, "label": "UTC+12", "value": "UTC+12"},
    {"offset": 13, "label": "UTC+13", "value": "UTC+13"},
    {"offset": 14, "label": "UTC+14", "value": "UTC+14"},
]

# Valid timezone values for validation
VALID_TIMEZONE_VALUES: List[str] = [tz["value"] for tz in TIMEZONE_OPTIONS]

# Timezone grid layout configuration (5x5 with UTC+0 centered)
TIMEZONE_GRID_CONFIG = {
    "rows": 5,
    "cols": 5,
    "center_offset": 0,  # UTC+0 should be in the center
    "center_row": 2,     # 0-indexed, so row 2 is the middle of 5 rows
    "center_col": 2,     # 0-indexed, so col 2 is the middle of 5 cols
}

def get_timezone_keyboard_layout() -> List[List[Dict[str, Any]]]:
    """
    Generate a 5x5 keyboard layout with UTC+0 centered.
    
    Returns:
        List of rows, each containing a list of timezone button data.
    """
    # Create a 5x5 grid
    grid = [[None for _ in range(5)] for _ in range(5)]
    
    # Place UTC+0 in the center
    center_row, center_col = 2, 2
    
    # Find UTC+0 in our timezone options
    utc_zero_index = next(i for i, tz in enumerate(TIMEZONE_OPTIONS) if tz["offset"] == 0)
    
    # Calculate the starting index for the grid
    # We want UTC+0 to be in the center, so we need to place timezones around it
    start_index = utc_zero_index - (center_row * 5 + center_col)
    
    # Fill the grid
    for row in range(5):
        for col in range(5):
            index = start_index + (row * 5 + col)
            if 0 <= index < len(TIMEZONE_OPTIONS):
                grid[row][col] = TIMEZONE_OPTIONS[index]
    
    # Filter out None values and return only valid timezones
    result = []
    for row in grid:
        filtered_row = [tz for tz in row if tz is not None]
        if filtered_row:  # Only add non-empty rows
            result.append(filtered_row)
    
    return result

def validate_timezone(timezone_value: str) -> bool:
    """
    Validate if a timezone value is valid.
    
    Args:
        timezone_value: The timezone value to validate
        
    Returns:
        True if valid, False otherwise
    """
    return timezone_value in VALID_TIMEZONE_VALUES

def get_timezone_display_name(timezone_value: str) -> str:
    """
    Get the display name for a timezone value.
    
    Args:
        timezone_value: The timezone value
        
    Returns:
        Display name or the original value if not found
    """
    for tz in TIMEZONE_OPTIONS:
        if tz["value"] == timezone_value:
            return tz["label"]
    return timezone_value
