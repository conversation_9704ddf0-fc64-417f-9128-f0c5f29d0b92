"""
Scheduling constants for the Zeitwahl AI Agent system.

This module contains scheduling-related keywords and patterns for
detecting scheduling intent in user messages.
"""

from typing import List, Set

# Primary scheduling keywords that strongly indicate scheduling intent
SCHEDULING_KEYWORDS: List[str] = [
    "schedule", "scheduling", "scheduled",
    "meeting", "meetings", "meet",
    "appointment", "appointments", "appt",
    "event", "events",
    "calendar", "calendars",
    "book", "booking", "booked",
    "reserve", "reservation", "reserved",
    "plan", "planning", "planned",
    "organize", "organizing", "organized",
    "arrange", "arranging", "arranged",
    "set up", "setup",
    "call", "calls", "calling",
    "conference", "conferences",
    "session", "sessions",
    "interview", "interviews",
    "consultation", "consultations",
    "reminder", "reminders", "remind",
    "deadline", "deadlines",
    "due date", "due dates",
    "availability", "available", "free time",
    "busy", "occupied",
    "reschedule", "rescheduling", "rescheduled",
    "cancel", "cancellation", "cancelled",
    "postpone", "postponed", "postponing",
    "delay", "delayed", "delaying",
]

# Time-related keywords that support scheduling context
TIME_KEYWORDS: List[str] = [
    "today", "tomorrow", "yesterday",
    "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
    "morning", "afternoon", "evening", "night",
    "am", "pm", "a.m.", "p.m.",
    "hour", "hours", "minute", "minutes",
    "week", "weeks", "month", "months", "year", "years",
    "next", "last", "this",
    "early", "late", "soon", "later",
    "before", "after", "during",
    "when", "what time", "how long",
    "start", "end", "begin", "finish",
    "from", "to", "until", "till",
    "at", "on", "in",
    "now", "asap", "immediately",
    "urgent", "priority", "important",
]

# Date patterns (regex-like patterns for detection)
DATE_PATTERNS: List[str] = [
    r"\d{1,2}/\d{1,2}/\d{2,4}",  # MM/DD/YYYY or DD/MM/YYYY
    r"\d{1,2}-\d{1,2}-\d{2,4}",  # MM-DD-YYYY or DD-MM-YYYY
    r"\d{1,2}\.\d{1,2}\.\d{2,4}",  # MM.DD.YYYY or DD.MM.YYYY
    r"\d{4}-\d{1,2}-\d{1,2}",  # YYYY-MM-DD
    r"\d{1,2}:\d{2}",  # HH:MM
    r"\d{1,2}:\d{2}:\d{2}",  # HH:MM:SS
]

# Action keywords that indicate scheduling actions
ACTION_KEYWORDS: List[str] = [
    "create", "add", "new", "make",
    "update", "edit", "modify", "change",
    "delete", "remove", "cancel",
    "find", "search", "look for", "check",
    "list", "show", "display", "view",
    "confirm", "accept", "approve",
    "decline", "reject", "deny",
    "move", "shift", "transfer",
    "copy", "duplicate", "repeat",
    "invite", "inviting", "invited",
    "attend", "attending", "join",
    "host", "hosting", "organize",
]

# Convert to sets for faster lookup
SCHEDULING_KEYWORDS_SET: Set[str] = set(SCHEDULING_KEYWORDS)
TIME_KEYWORDS_SET: Set[str] = set(TIME_KEYWORDS)
ACTION_KEYWORDS_SET: Set[str] = set(ACTION_KEYWORDS)

# Combined keywords for comprehensive detection
ALL_SCHEDULING_KEYWORDS: Set[str] = SCHEDULING_KEYWORDS_SET | TIME_KEYWORDS_SET | ACTION_KEYWORDS_SET

def has_scheduling_keywords(message_text: str, threshold: int = 1) -> bool:
    """
    Check if a message contains scheduling-related keywords.
    
    Args:
        message_text: The message text to analyze
        threshold: Minimum number of keywords required (default: 1)
        
    Returns:
        True if message contains scheduling keywords above threshold
    """
    if not message_text:
        return False
    
    # Convert to lowercase for case-insensitive matching
    text_lower = message_text.lower()
    
    # Count matches
    keyword_count = 0
    
    # Check primary scheduling keywords (higher weight)
    for keyword in SCHEDULING_KEYWORDS:
        if keyword in text_lower:
            keyword_count += 2  # Primary keywords count double
    
    # Check time keywords
    for keyword in TIME_KEYWORDS:
        if keyword in text_lower:
            keyword_count += 1
    
    # Check action keywords
    for keyword in ACTION_KEYWORDS:
        if keyword in text_lower:
            keyword_count += 1
    
    return keyword_count >= threshold

def get_scheduling_confidence_score(message_text: str) -> float:
    """
    Calculate a confidence score (0.0 to 1.0) for scheduling intent.
    
    Args:
        message_text: The message text to analyze
        
    Returns:
        Confidence score between 0.0 and 1.0
    """
    if not message_text:
        return 0.0
    
    text_lower = message_text.lower()
    words = text_lower.split()
    total_words = len(words)
    
    if total_words == 0:
        return 0.0
    
    # Count different types of keywords
    primary_matches = sum(1 for keyword in SCHEDULING_KEYWORDS if keyword in text_lower)
    time_matches = sum(1 for keyword in TIME_KEYWORDS if keyword in text_lower)
    action_matches = sum(1 for keyword in ACTION_KEYWORDS if keyword in text_lower)
    
    # Calculate weighted score
    primary_weight = 0.6
    time_weight = 0.3
    action_weight = 0.1
    
    # Normalize by message length (but cap the effect)
    length_factor = min(total_words / 10.0, 1.0)  # Cap at 10 words
    
    score = (
        (primary_matches * primary_weight) +
        (time_matches * time_weight) +
        (action_matches * action_weight)
    ) * length_factor
    
    # Ensure score is between 0.0 and 1.0
    return min(score, 1.0)

def extract_scheduling_keywords(message_text: str) -> List[str]:
    """
    Extract all scheduling-related keywords found in the message.
    
    Args:
        message_text: The message text to analyze
        
    Returns:
        List of found keywords
    """
    if not message_text:
        return []
    
    text_lower = message_text.lower()
    found_keywords = []
    
    # Check all keyword sets
    for keyword in ALL_SCHEDULING_KEYWORDS:
        if keyword in text_lower:
            found_keywords.append(keyword)
    
    return found_keywords
