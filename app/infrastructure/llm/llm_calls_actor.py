"""
LLM Calls Actor for handling LLM API calls and token management.

This actor wraps the LLMService and TokenCalculator, handles LLM API calls,
token calculations, and context length management.
"""

import logging
import time
from typing import Dict, Any, Optional

from app.core.actors.actor import Actor
from app.core.events.event_bus import event_bus
from app.core.context.message_context import message_context_manager, MessageState
from .services.llm_service import LLMService
from .services.token_calculator import TokenCalculator
from app.core.events.events import (
    LLMRequestReady, LLMResponseReady, SendMessageRequest,
    ErrorOccurred
)

logger = logging.getLogger(__name__)


class LLMCallsActor(Actor):
    """Actor responsible for LLM API calls and token management."""

    def __init__(
        self,
        name: Optional[str] = None,
        llm_service: Optional[LLMService] = None,
        token_calculator: Optional[TokenCalculator] = None
    ):
        super().__init__(name or "LLMCallsActor")

        # Use dependency injection if provided, otherwise create instances
        self.llm_service: LLMService = llm_service or LLMService()
        self.token_calculator: TokenCalculator = token_calculator or TokenCalculator()

    async def initialize(self) -> None:
        """Initialize the LLM calls actor."""
        try:
            logger.info(f"🔧 Initializing {self.name}")

            # Services are already initialized via dependency injection or constructor
            # This method can be used for any additional async initialization if needed

            logger.info(f"✅ {self.name} initialized successfully")

        except Exception as e:
            logger.error(f"💥 Failed to initialize {self.name}: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup the LLM calls actor."""
        logger.info(f"🧹 {self.name} cleanup completed")
    
    @event_bus.subscribe("llm_request_ready")
    async def handle_llm_request(self, event_data: LLMRequestReady) -> None:
        """Handle LLM processing requests."""
        try:
            logger.debug(f"Processing LLM request for user {event_data.telegram_user_id} with context_id {event_data.context_id}")

            # Get the message context
            context = message_context_manager.get_context(event_data.context_id)
            if not context:
                logger.error(f"Context not found for context_id {event_data.context_id}")
                return

            # Update context state to LLM processing
            context.update_state(MessageState.LLM_PROCESSING, self.name)

            # Get the primary provider
            primary_provider = self.llm_service.primary_provider

            # Calculate tokens and optimize prompt if needed
            optimized_prompt, optimization_info = self.token_calculator.optimize_prompt_length(
                event_data.prompt,
                primary_provider,
                max_response_tokens=event_data.max_tokens or 1000
            )

            if optimization_info["was_truncated"]:
                logger.info(f"Prompt truncated from {optimization_info['original_tokens']} to {optimization_info['optimized_tokens']} tokens")

            # Update message status to processing
            await event_bus.publish("update_message_status_request", {
                "context_id": event_data.context_id,
                "telegram_message_id": event_data.telegram_message_id,
                "chat_id": event_data.chat_id,
                "status": "processing"
            })
            
            # Make the LLM call
            start_time = time.time()
            
            try:
                response = await self.llm_service.generate_response(
                    prompt=optimized_prompt,
                    temperature=event_data.temperature or 0.7,
                    max_tokens=event_data.max_tokens or 1000
                )
                
                processing_time = time.time() - start_time
                
                # Extract response details
                response_text = response.get("content", "")
                provider_used = response.get("provider", primary_provider)
                tokens_used = response.get("tokens_used", 0)
                
                # If no tokens reported, estimate them
                if tokens_used == 0:
                    input_tokens = self.token_calculator.estimate_tokens(optimized_prompt)
                    output_tokens = self.token_calculator.estimate_tokens(response_text)
                    tokens_used = input_tokens + output_tokens
                
                # Update context with LLM response
                context.llm_response = response_text
                context.update_state(MessageState.LLM_PROCESSED, self.name, {
                    "provider_used": provider_used,
                    "tokens_used": tokens_used,
                    "processing_time": processing_time
                })

                # Publish successful LLM response
                await event_bus.publish("llm_response_ready", LLMResponseReady(
                    context_id=event_data.context_id,
                    telegram_user_id=event_data.telegram_user_id,
                    chat_id=event_data.chat_id,
                    telegram_message_id=event_data.telegram_message_id,
                    response_text=response_text,
                    provider_used=provider_used,
                    tokens_used=tokens_used,
                    processing_time=processing_time,
                    success=True
                ))

                # Send the response to the user
                await event_bus.publish("send_message_request", SendMessageRequest(
                    chat_id=event_data.chat_id,
                    message_text=response_text,
                    context_id=event_data.context_id,
                    reply_to_message_id=event_data.telegram_message_id
                ))
                
                # Update message status to processed
                await event_bus.publish("update_message_status_request", {
                    "context_id": event_data.context_id,
                    "telegram_message_id": event_data.telegram_message_id,
                    "chat_id": event_data.chat_id,
                    "status": "processed"
                })
                
                logger.info(f"LLM request processed successfully for user {event_data.telegram_user_id} in {processing_time:.2f}s")
                
            except Exception as llm_error:
                processing_time = time.time() - start_time
                
                logger.error(f"LLM call failed: {llm_error}")
                
                # Try fallback providers
                fallback_response = await self._try_fallback_providers(
                    optimized_prompt, event_data
                )
                
                if fallback_response:
                    # Fallback succeeded
                    await event_bus.publish("llm_response_ready", LLMResponseReady(
                        context_id=event_data.context_id,
                        telegram_user_id=event_data.telegram_user_id,
                        chat_id=event_data.chat_id,
                        telegram_message_id=event_data.telegram_message_id,
                        response_text=fallback_response["content"],
                        provider_used=fallback_response["provider"],
                        tokens_used=fallback_response.get("tokens_used", 0),
                        processing_time=processing_time,
                        success=True
                    ))
                    
                    # Send the response
                    await event_bus.publish("send_message_request", SendMessageRequest(
                        chat_id=event_data.chat_id,
                        message_text=fallback_response["content"],
                        reply_to_message_id=event_data.telegram_message_id
                    ))
                    
                    # Update message status
                    await event_bus.publish("update_message_status_request", {
                        "context_id": event_data.context_id,
                        "telegram_message_id": event_data.telegram_message_id,
                        "chat_id": event_data.chat_id,
                        "status": "processed"
                    })
                    
                else:
                    # All providers failed
                    await self._handle_llm_failure(event_data, str(llm_error))
                
        except Exception as e:
            logger.error(f"Error in LLM request handling: {e}")
            await self._handle_llm_failure(event_data, str(e))
    
    async def _try_fallback_providers(
        self, 
        prompt: str, 
        event_data: LLMRequestReady
    ) -> Optional[Dict[str, Any]]:
        """Try fallback providers if primary provider fails."""
        try:
            fallback_providers = self.llm_service.fallback_providers
            
            for provider_name in fallback_providers:
                if provider_name in self.llm_service.providers:
                    try:
                        logger.info(f"Trying fallback provider: {provider_name}")
                        
                        # Use the fallback provider
                        original_primary = self.llm_service.primary_provider
                        self.llm_service.primary_provider = provider_name
                        
                        response = await self.llm_service.generate_response(
                            prompt=prompt,
                            temperature=event_data.temperature or 0.7,
                            max_tokens=event_data.max_tokens or 1000
                        )
                        
                        # Restore original primary provider
                        self.llm_service.primary_provider = original_primary
                        
                        logger.info(f"Fallback provider {provider_name} succeeded")
                        return response
                        
                    except Exception as fallback_error:
                        logger.warning(f"Fallback provider {provider_name} failed: {fallback_error}")
                        continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error trying fallback providers: {e}")
            return None
    
    async def _handle_llm_failure(self, event_data: LLMRequestReady, error_message: str):
        """Handle LLM processing failures."""
        try:
            # Publish failed LLM response
            await event_bus.publish("llm_response_ready", LLMResponseReady(
                context_id=event_data.context_id,
                telegram_user_id=event_data.telegram_user_id,
                chat_id=event_data.chat_id,
                telegram_message_id=event_data.telegram_message_id,
                response_text="",
                provider_used="none",
                tokens_used=0,
                processing_time=0.0,
                success=False,
                error_message=error_message
            ))
            
            # Send error message to user
            error_response = """I apologize, but I'm experiencing technical difficulties right now. Please try again in a few moments.

If the problem persists, you can:
• Try rephrasing your request
• Contact support for assistance
• Use /help to see available commands"""
            
            await event_bus.publish("send_message_request", SendMessageRequest(
                chat_id=event_data.chat_id,
                message_text=error_response,
                reply_to_message_id=event_data.telegram_message_id
            ))
            
            # Update message status to error
            await event_bus.publish("update_message_status_request", {
                "context_id": event_data.context_id,
                "telegram_message_id": event_data.telegram_message_id,
                "chat_id": event_data.chat_id,
                "status": "error"
            })
            
            # Publish error event
            await event_bus.publish("error_occurred", ErrorOccurred(
                user_id=event_data.telegram_user_id,
                chat_id=event_data.chat_id,
                message_id=event_data.telegram_message_id,
                error_type="llm_error",
                error_message=error_message,
                component=f"{self.name}.handle_llm_request"
            ))
            
        except Exception as e:
            logger.error(f"Error handling LLM failure: {e}")
    
    @event_bus.subscribe("token_calculation_request")
    async def handle_token_calculation(self, event_data: Dict[str, Any]) -> None:
        """Handle token calculation requests."""
        try:
            text = event_data.get("text", "")
            model_name = event_data.get("model_name", "default")
            
            # Calculate tokens
            token_count = self.token_calculator.estimate_tokens(text)
            model_limit = self.token_calculator.get_model_limit(model_name)
            remaining_tokens = self.token_calculator.calculate_remaining_tokens(
                text, model_name
            )
            
            # Publish token calculation response
            await event_bus.publish("token_calculation_response", {
                "request_id": event_data.get("request_id"),
                "token_count": token_count,
                "model_limit": model_limit,
                "remaining_tokens": remaining_tokens,
                "model_name": model_name
            })
            
        except Exception as e:
            logger.error(f"Error in token calculation: {e}")
            await event_bus.publish("token_calculation_response", {
                "request_id": event_data.get("request_id"),
                "error": str(e)
            })
