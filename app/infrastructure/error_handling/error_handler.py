"""
Error handling utilities for robust error management.

This module provides utilities for handling specific errors from MongoDB,
Telegram, and other services with appropriate retry logic and logging.
"""

import asyncio
import logging
from typing import Any, Callable, Optional, Type, Union
from functools import wraps

# MongoDB error imports
try:
    from pymongo.errors import (
        NetworkTimeout, AutoReconnect, OperationCancelled,
        ConnectionFailure, ServerSelectionTimeoutError
    )
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False
    # Define dummy classes for type hints
    class NetworkTimeout(Exception): pass
    class AutoReconnect(Exception): pass
    class OperationCancelled(Exception): pass
    class ConnectionFailure(Exception): pass
    class ServerSelectionTimeoutError(Exception): pass

# Telegram error imports
try:
    from aiogram.exceptions import TelegramNetworkError, TelegramRetryAfter
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    # Define dummy classes for type hints
    class TelegramNetworkError(Exception): pass
    class TelegramRetryAfter(Exception): pass

logger = logging.getLogger(__name__)


class ErrorHandler:
    """Centralized error handler for different types of errors."""
    
    @staticmethod
    def is_mongodb_connection_error(error: Exception) -> bool:
        """Check if error is a MongoDB connection-related error."""
        if not MONGODB_AVAILABLE:
            return False
        
        return isinstance(error, (
            NetworkTimeout, AutoReconnect, OperationCancelled,
            ConnectionFailure, ServerSelectionTimeoutError
        ))
    
    @staticmethod
    def is_telegram_network_error(error: Exception) -> bool:
        """Check if error is a Telegram network-related error."""
        if not TELEGRAM_AVAILABLE:
            return False
        
        return isinstance(error, (TelegramNetworkError, TelegramRetryAfter))
    
    @staticmethod
    def is_retryable_error(error: Exception) -> bool:
        """Check if error is retryable."""
        return (
            ErrorHandler.is_mongodb_connection_error(error) or
            ErrorHandler.is_telegram_network_error(error)
        )
    
    @staticmethod
    def get_retry_delay(error: Exception, attempt: int) -> float:
        """Get appropriate retry delay for the error type."""
        if isinstance(error, TelegramRetryAfter):
            return getattr(error, 'retry_after', 1.0)
        
        # Exponential backoff with jitter
        base_delay = min(2 ** attempt, 60)  # Cap at 60 seconds
        return base_delay + (attempt * 0.1)  # Add small jitter
    
    @staticmethod
    def should_continue_retrying(error: Exception, attempt: int, max_attempts: int) -> bool:
        """Determine if we should continue retrying."""
        if attempt >= max_attempts:
            return False
        
        # Don't retry certain critical errors
        if isinstance(error, OperationCancelled):
            return False
        
        return ErrorHandler.is_retryable_error(error)


def with_retry(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    exponential_backoff: bool = True,
    handle_specific_errors: bool = True
):
    """
    Decorator for adding retry logic to async functions.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Base delay between retries in seconds
        exponential_backoff: Whether to use exponential backoff
        handle_specific_errors: Whether to handle specific error types
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_error = None
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                
                except Exception as e:
                    last_error = e
                    
                    # Log the error
                    if attempt == 0:
                        logger.warning(f"Error in {func.__name__}: {e}")
                    else:
                        logger.warning(f"Retry {attempt} failed for {func.__name__}: {e}")
                    
                    # Check if we should retry
                    if handle_specific_errors:
                        if not ErrorHandler.should_continue_retrying(e, attempt, max_attempts):
                            logger.error(f"Not retrying {func.__name__} due to error type: {e}")
                            raise
                    
                    # Don't sleep on the last attempt
                    if attempt < max_attempts - 1:
                        if handle_specific_errors:
                            delay = ErrorHandler.get_retry_delay(e, attempt)
                        else:
                            delay = base_delay * (2 ** attempt if exponential_backoff else 1)
                        
                        logger.info(f"Retrying {func.__name__} in {delay:.1f} seconds...")
                        await asyncio.sleep(delay)
            
            # All attempts failed
            logger.error(f"All {max_attempts} attempts failed for {func.__name__}")
            raise last_error
        
        return wrapper
    return decorator


def with_mongodb_retry(max_attempts: int = 5):
    """Decorator specifically for MongoDB operations."""
    return with_retry(
        max_attempts=max_attempts,
        base_delay=1.0,
        exponential_backoff=True,
        handle_specific_errors=True
    )


def with_telegram_retry(max_attempts: int = 3):
    """Decorator specifically for Telegram operations."""
    return with_retry(
        max_attempts=max_attempts,
        base_delay=0.5,
        exponential_backoff=True,
        handle_specific_errors=True
    )


class RobustService:
    """Base class for services that need robust error handling."""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = logging.getLogger(f"{__name__}.{service_name}")
        self._is_healthy = True
        self._consecutive_failures = 0
        self._max_consecutive_failures = 5
    
    def mark_healthy(self):
        """Mark service as healthy."""
        if not self._is_healthy:
            self.logger.info(f"🟢 {self.service_name} service recovered")
        self._is_healthy = True
        self._consecutive_failures = 0
    
    def mark_unhealthy(self, error: Exception):
        """Mark service as unhealthy."""
        self._consecutive_failures += 1
        
        if self._is_healthy and self._consecutive_failures >= self._max_consecutive_failures:
            self._is_healthy = False
            self.logger.error(f"🔴 {self.service_name} service marked as unhealthy after {self._consecutive_failures} failures")
        
        self.logger.warning(f"⚠️ {self.service_name} failure #{self._consecutive_failures}: {error}")
    
    @property
    def is_healthy(self) -> bool:
        """Check if service is healthy."""
        return self._is_healthy
    
    async def execute_with_health_check(self, operation: Callable, *args, **kwargs) -> Any:
        """Execute operation with health checking."""
        try:
            result = await operation(*args, **kwargs)
            self.mark_healthy()
            return result
        except Exception as e:
            self.mark_unhealthy(e)
            raise


# Specific error handling functions
async def handle_mongodb_error(error: Exception, operation_name: str) -> None:
    """Handle MongoDB-specific errors with appropriate logging."""
    if isinstance(error, NetworkTimeout):
        logger.warning(f"🔄 MongoDB network timeout in {operation_name}: {error}")
    elif isinstance(error, AutoReconnect):
        logger.warning(f"🔄 MongoDB auto-reconnect in {operation_name}: {error}")
    elif isinstance(error, OperationCancelled):
        logger.error(f"❌ MongoDB operation cancelled in {operation_name}: {error}")
    elif isinstance(error, ConnectionFailure):
        logger.error(f"🔌 MongoDB connection failure in {operation_name}: {error}")
    elif isinstance(error, ServerSelectionTimeoutError):
        logger.error(f"⏰ MongoDB server selection timeout in {operation_name}: {error}")
    else:
        logger.error(f"💥 Unknown MongoDB error in {operation_name}: {error}")


async def handle_telegram_error(error: Exception, operation_name: str) -> None:
    """Handle Telegram-specific errors with appropriate logging."""
    if isinstance(error, TelegramNetworkError):
        logger.warning(f"🔄 Telegram network error in {operation_name}: {error}")
    elif isinstance(error, TelegramRetryAfter):
        retry_after = getattr(error, 'retry_after', 1)
        logger.warning(f"⏳ Telegram rate limit in {operation_name}, retry after {retry_after}s: {error}")
    else:
        logger.error(f"💥 Unknown Telegram error in {operation_name}: {error}")
