"""
UserChat Repository for database operations.

This module provides database operations for UserChat entities,
handling the relationship between users and chats.
"""

import logging
from typing import List, Optional
from datetime import datetime, timezone, timedelta

from ..models.user_chat import UserChat
from ..connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class UserChatRepository:
    """Repository for UserChat database operations."""
    
    def __init__(self):
        self.connection_manager = get_connection_manager()
        self.collection_name = "user_chats"
    
    async def create_user_chat(self, user_chat: UserChat) -> UserChat:
        """Create a new user-chat relationship."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Convert to dict for MongoDB
            user_chat_dict = user_chat.to_dict()
            
            # Insert the user-chat relationship
            result = collection.insert_one(user_chat_dict)
            
            # Update with the generated ID
            user_chat.id = result.inserted_id
            
            logger.debug(f"Created user-chat relationship for user {user_chat.telegram_user_id} in chat {user_chat.chat_id}")
            return user_chat
            
        except Exception as e:
            logger.error(f"Error creating user-chat relationship: {e}")
            raise
    
    async def get_user_chat(self, telegram_user_id: int, chat_id: int) -> Optional[UserChat]:
        """Get a user-chat relationship by user ID and chat ID."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Find the user-chat relationship
            user_chat_doc = collection.find_one({
                "telegram_user_id": telegram_user_id,
                "chat_id": chat_id
            })
            
            if user_chat_doc:
                return UserChat.from_dict(user_chat_doc)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user-chat relationship for user {telegram_user_id} in chat {chat_id}: {e}")
            raise
    
    async def update_user_chat(self, user_chat: UserChat) -> UserChat:
        """Update an existing user-chat relationship."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Update the user-chat relationship
            user_chat_dict = user_chat.to_dict()
            user_chat_dict["updated_at"] = datetime.now(timezone.utc)
            
            result = collection.update_one(
                {
                    "telegram_user_id": user_chat.telegram_user_id,
                    "chat_id": user_chat.chat_id
                },
                {"$set": user_chat_dict}
            )
            
            if result.matched_count == 0:
                raise ValueError(f"User-chat relationship not found for user {user_chat.telegram_user_id} in chat {user_chat.chat_id}")
            
            logger.debug(f"Updated user-chat relationship for user {user_chat.telegram_user_id} in chat {user_chat.chat_id}")
            return user_chat
            
        except Exception as e:
            logger.error(f"Error updating user-chat relationship: {e}")
            raise
    
    async def get_user_chats(self, telegram_user_id: int, active_only: bool = True) -> List[UserChat]:
        """Get all chats for a user."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            query = {"telegram_user_id": telegram_user_id}
            if active_only:
                query["is_active"] = True
            
            cursor = collection.find(query).sort("last_message_at", -1)
            
            user_chats = []
            for user_chat_doc in cursor:
                user_chats.append(UserChat.from_dict(user_chat_doc))
            
            return user_chats
            
        except Exception as e:
            logger.error(f"Error getting user chats for user {telegram_user_id}: {e}")
            raise
    
    async def get_chat_users(self, chat_id: int, active_only: bool = True) -> List[UserChat]:
        """Get all users in a chat."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            query = {"chat_id": chat_id}
            if active_only:
                query["is_active"] = True
            
            cursor = collection.find(query).sort("last_message_at", -1)
            
            user_chats = []
            for user_chat_doc in cursor:
                user_chats.append(UserChat.from_dict(user_chat_doc))
            
            return user_chats
            
        except Exception as e:
            logger.error(f"Error getting chat users for chat {chat_id}: {e}")
            raise
    
    async def update_activity(self, telegram_user_id: int, chat_id: int) -> bool:
        """Update activity for a user-chat relationship."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            now = datetime.now(timezone.utc)
            result = collection.update_one(
                {
                    "telegram_user_id": telegram_user_id,
                    "chat_id": chat_id
                },
                {
                    "$set": {
                        "last_message_at": now,
                        "updated_at": now
                    },
                    "$inc": {"message_count": 1}
                }
            )
            
            return result.matched_count > 0
            
        except Exception as e:
            logger.error(f"Error updating activity for user {telegram_user_id} in chat {chat_id}: {e}")
            raise
    
    async def create_or_update_user_chat(
        self,
        telegram_user_id: int,
        chat_id: int,
        chat_type: str = "private",
        chat_title: Optional[str] = None
    ) -> UserChat:
        """Create or update a user-chat relationship."""
        try:
            # Try to get existing relationship
            existing = await self.get_user_chat(telegram_user_id, chat_id)
            
            if existing:
                # Update existing relationship
                existing.update_activity()
                if chat_title and existing.chat_title != chat_title:
                    existing.chat_title = chat_title
                if existing.chat_type != chat_type:
                    existing.chat_type = chat_type
                
                return await self.update_user_chat(existing)
            else:
                # Create new relationship
                new_user_chat = UserChat.create_from_message(
                    telegram_user_id=telegram_user_id,
                    chat_id=chat_id,
                    chat_type=chat_type,
                    chat_title=chat_title
                )
                return await self.create_user_chat(new_user_chat)
                
        except Exception as e:
            logger.error(f"Error creating or updating user-chat relationship: {e}")
            raise
    
    async def get_active_chats_for_user(self, telegram_user_id: int) -> List[int]:
        """Get list of active chat IDs for a user."""
        try:
            user_chats = await self.get_user_chats(telegram_user_id, active_only=True)
            return [uc.chat_id for uc in user_chats]
            
        except Exception as e:
            logger.error(f"Error getting active chats for user {telegram_user_id}: {e}")
            raise
    
    async def delete_user_chat(self, telegram_user_id: int, chat_id: int) -> bool:
        """Delete a user-chat relationship."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            result = collection.delete_one({
                "telegram_user_id": telegram_user_id,
                "chat_id": chat_id
            })
            
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting user-chat relationship: {e}")
            raise
