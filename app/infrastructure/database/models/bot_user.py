"""
BotUser entity model for MongoDB storage.

This module defines the BotUser class that represents a Telegram user
with all necessary information for unique identification and user management.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId


class BotUser(BaseModel):
    """
    BotUser entity representing a Telegram user in the system.
    
    This class contains all the required information to uniquely identify
    a user from Telegram messages and track their interactions.
    """
    
    # MongoDB document ID
    id: Optional[ObjectId] = Field(default=None, alias="_id")
    
    # Telegram user identification (primary key)
    telegram_user_id: int = Field(..., description="Unique Telegram user ID")
    
    # User profile information from Telegram
    username: Optional[str] = Field(default=None, description="Telegram username (without @)")
    first_name: Optional[str] = Field(default=None, description="User's first name")
    last_name: Optional[str] = Field(default=None, description="User's last name")
    language_code: Optional[str] = Field(default=None, description="User's language code")
    
    # User settings
    timezone: str = Field(default="UTC", description="User's timezone")
    
    # User activity tracking
    is_active: bool = Field(default=True, description="Whether the user is active")
    is_bot: bool = Field(default=False, description="Whether this user is a bot")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="User creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Last update timestamp")
    last_seen_at: Optional[datetime] = Field(default=None, description="Last activity timestamp")
    
    # Message tracking
    total_messages: int = Field(default=0, description="Total number of messages sent")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional user metadata")
    
    class Config:
        """Pydantic configuration."""
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    
    def update_last_seen(self) -> None:
        """Update the last seen timestamp to current time."""
        self.last_seen_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)

    def add_chat(self, chat_id: int) -> None:
        """Deprecated: Use UserChat entity instead."""
        # This method is kept for backward compatibility but does nothing
        # Chat relationships are now managed through UserChat entity
        self.updated_at = datetime.now(timezone.utc)

    def increment_message_count(self) -> None:
        """Increment the total message count."""
        self.total_messages += 1
        self.updated_at = datetime.now(timezone.utc)

    def update_profile(self, username: Optional[str] = None,
                      first_name: Optional[str] = None,
                      last_name: Optional[str] = None,
                      language_code: Optional[str] = None) -> None:
        """Update user profile information."""
        if username is not None:
            self.username = username
        if first_name is not None:
            self.first_name = first_name
        if last_name is not None:
            self.last_name = last_name
        if language_code is not None:
            self.language_code = language_code

        self.updated_at = datetime.now(timezone.utc)
    
    def get_display_name(self) -> str:
        """Get a display name for the user."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return f"@{self.username}"
        else:
            return f"User {self.telegram_user_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary for MongoDB storage."""
        data = self.model_dump(by_alias=True, exclude_none=True)
        if self.id:
            data["_id"] = self.id
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BotUser":
        """Create a BotUser instance from a dictionary."""
        if "_id" in data:
            data["id"] = data["_id"]
        return cls(**data)
