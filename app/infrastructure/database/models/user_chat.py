"""
UserChat entity model for MongoDB storage.

This module defines the UserChat class that represents the relationship
between users and chats, tracking user activity and participation in different chats.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId


class UserChat(BaseModel):
    """
    UserChat entity representing a user's participation in a chat.
    
    This entity tracks the relationship between users and chats,
    including activity metrics and participation details.
    """
    
    # MongoDB document ID
    id: Optional[ObjectId] = Field(default=None, alias="_id")
    
    # User and chat identification
    telegram_user_id: int = Field(..., description="Telegram user ID")
    chat_id: int = Field(..., description="Telegram chat ID")
    
    # Chat information
    chat_type: str = Field(default="private", description="Type of chat (private, group, supergroup, channel)")
    chat_title: Optional[str] = Field(default=None, description="Title of the chat (for groups)")
    
    # User role and status in chat
    user_status: str = Field(default="member", description="User status in chat (member, admin, creator, left, kicked)")
    is_active: bool = Field(default=True, description="Whether user is active in this chat")
    
    # Activity tracking
    first_message_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When user first sent a message in this chat")
    last_message_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When user last sent a message in this chat")
    message_count: int = Field(default=0, description="Total messages sent by user in this chat")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When user joined/was added to chat")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Last update timestamp")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional chat-specific metadata")
    
    class Config:
        """Pydantic configuration."""
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    
    def update_activity(self) -> None:
        """Update activity timestamps and increment message count."""
        now = datetime.now(timezone.utc)
        self.last_message_at = now
        self.updated_at = now
        self.message_count += 1
    
    def set_inactive(self) -> None:
        """Mark user as inactive in this chat."""
        self.is_active = False
        self.updated_at = datetime.now(timezone.utc)
    
    def set_active(self) -> None:
        """Mark user as active in this chat."""
        self.is_active = True
        self.updated_at = datetime.now(timezone.utc)
    
    def update_status(self, status: str) -> None:
        """Update user status in chat."""
        self.user_status = status
        self.updated_at = datetime.now(timezone.utc)
        
        # If user left or was kicked, mark as inactive
        if status in ["left", "kicked"]:
            self.set_inactive()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for MongoDB storage."""
        data = self.model_dump(by_alias=True, exclude_none=True)
        if self.id:
            data["_id"] = self.id
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserChat":
        """Create instance from dictionary."""
        if "_id" in data:
            data["id"] = data.pop("_id")
        return cls(**data)
    
    @classmethod
    def create_from_message(
        cls,
        telegram_user_id: int,
        chat_id: int,
        chat_type: str = "private",
        chat_title: Optional[str] = None
    ) -> "UserChat":
        """Create a UserChat instance from message data."""
        return cls(
            telegram_user_id=telegram_user_id,
            chat_id=chat_id,
            chat_type=chat_type,
            chat_title=chat_title,
            message_count=1  # First message
        )
    
    def get_activity_summary(self) -> Dict[str, Any]:
        """Get activity summary for this user-chat relationship."""
        now = datetime.now(timezone.utc)
        days_since_first = (now - self.first_message_at).days
        days_since_last = (now - self.last_message_at).days
        
        return {
            "chat_id": self.chat_id,
            "chat_type": self.chat_type,
            "chat_title": self.chat_title,
            "is_active": self.is_active,
            "user_status": self.user_status,
            "message_count": self.message_count,
            "days_since_first_message": days_since_first,
            "days_since_last_message": days_since_last,
            "messages_per_day": self.message_count / max(days_since_first, 1)
        }
