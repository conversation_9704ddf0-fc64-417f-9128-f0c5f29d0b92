2025-07-02 05:26:41 - 🤖 app.infrastructure.telegram.bot_actor - 152- INFO - message_id=44 date=datetime.datetime(2025, 7, 2, 3, 26, 1, tzinfo=TzInfo(UTC)) chat=Chat(id=7704389472, type='private', title=None, username=None, first_name='MOji<PERSON>', last_name=None, is_forum=None, accent_color_id=None, active_usernames=None, available_reactions=None, background_custom_emoji_id=None, bio=None, birthdate=None, business_intro=None, business_location=None, business_opening_hours=None, can_set_sticker_set=None, custom_emoji_sticker_set_name=None, description=None, emoji_status_custom_emoji_id=None, emoji_status_expiration_date=None, has_aggressive_anti_spam_enabled=None, has_hidden_members=None, has_private_forwards=None, has_protected_content=None, has_restricted_voice_and_video_messages=None, has_visible_history=None, invite_link=None, join_by_request=None, join_to_send_messages=None, linked_chat_id=None, location=None, message_auto_delete_time=None, permissions=None, personal_chat=None, photo=None, pinned_message=None, profile_accent_color_id=None, profile_background_custom_emoji_id=None, slow_mode_delay=None, sticker_set_name=None, unrestrict_boost_count=None) message_thread_id=None from_user=User(id=7704389472, is_bot=False, first_name='MOjii', last_name=None, username=None, language_code='en', is_premium=None, added_to_attachment_menu=None, can_join_groups=None, can_read_all_group_messages=None, supports_inline_queries=None, can_connect_to_business=None, has_main_web_app=None) sender_chat=None sender_boost_count=None sender_business_bot=None business_connection_id=None forward_origin=None is_topic_message=None is_automatic_forward=None reply_to_message=None external_reply=None quote=None reply_to_story=None via_bot=None edit_date=None has_protected_content=None is_from_offline=None media_group_id=None author_signature=None paid_star_count=None text='What now ?' entities=None link_preview_options=None effect_id=None animation=None audio=None document=None paid_media=None photo=None sticker=None story=None video=None video_note=None voice=None caption=None caption_entities=None show_caption_above_media=None has_media_spoiler=None contact=None dice=None game=None poll=None venue=None location=None new_chat_members=None left_chat_member=None new_chat_title=None new_chat_photo=None delete_chat_photo=None group_chat_created=None supergroup_chat_created=None channel_chat_created=None message_auto_delete_timer_changed=None migrate_to_chat_id=None migrate_from_chat_id=None pinned_message=None invoice=None successful_payment=None refunded_payment=None users_shared=None chat_shared=None gift=None unique_gift=None connected_website=None write_access_allowed=None passport_data=None proximity_alert_triggered=None boost_added=None chat_background_set=None forum_topic_created=None forum_topic_edited=None forum_topic_closed=None forum_topic_reopened=None general_forum_topic_hidden=None general_forum_topic_unhidden=None giveaway_created=None giveaway=None giveaway_winners=None giveaway_completed=None paid_message_price_changed=None video_chat_scheduled=None video_chat_started=None video_chat_ended=None video_chat_participants_invited=None web_app_data=None reply_markup=None forward_date=None forward_from=None forward_from_chat=None forward_from_message_id=None forward_sender_name=None forward_signature=None user_shared=None
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 157- DEBUG - Created message context b0213fcd-503b-4138-8245-f091a196c2d5 for message 44
2025-07-02 05:26:41 - 🤖 app.infrastructure.telegram.bot_actor - 182- DEBUG - Published message from user 7704389472 with context_id b0213fcd-503b-4138-8245-f091a196c2d5
2025-07-02 05:26:41 - 🔄 app.domain.preprocessing.preprocessing_actor - 58- DEBUG - Processing message from user 7704389472 with context_id b0213fcd-503b-4138-8245-f091a196c2d5
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 74- INFO - 📝 Context b0213fcd state changed: received → user_identifying by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 75- DEBUG - Message b0213fcd-503b-4138-8245-f091a196c2d5 state updated to user_identifying by PreprocessingActor
2025-07-02 05:26:41 - 🗄️ app.infrastructure.database.db_actor - 70- DEBUG - Identifying user 7704389472 with context_id b0213fcd-503b-4138-8245-f091a196c2d5
2025-07-02 05:26:41 - 🤖 app.infrastructure.database.repositories.bot_user_repository - 80- DEBUG - Updated user 7704389472
2025-07-02 05:26:41 - 🗄️ app.infrastructure.database.db_actor - 174- DEBUG - Storing message with context_id b0213fcd-503b-4138-8245-f091a196c2d5
2025-07-02 05:26:41 - 🗄️ app.infrastructure.database.repositories.conversation_message_repository - 39- DEBUG - Created message 44
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 74- INFO - 📝 Context b0213fcd state changed: user_identifying → user_identified by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 75- DEBUG - Message b0213fcd-503b-4138-8245-f091a196c2d5 state updated to user_identified by PreprocessingActor
2025-07-02 05:26:41 - 🔄 app.domain.preprocessing.preprocessing_actor - 146- DEBUG - User 7704389472 identified, proceeding to prevalidation
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 74- INFO - 📝 Context b0213fcd state changed: user_identified → validating by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 75- DEBUG - Message b0213fcd-503b-4138-8245-f091a196c2d5 state updated to validating by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 74- INFO - 📝 Context b0213fcd state changed: validating → validated by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 75- DEBUG - Message b0213fcd-503b-4138-8245-f091a196c2d5 state updated to validated by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 74- INFO - 📝 Context b0213fcd state changed: validated → context_building by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 75- DEBUG - Message b0213fcd-503b-4138-8245-f091a196c2d5 state updated to context_building by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 74- INFO - 📝 Context b0213fcd state changed: context_building → context_built by PreprocessingActor
2025-07-02 05:26:41 - 🏗️ app.core.context.message_context - 75- DEBUG - Message b0213fcd-503b-4138-8245-f091a196c2d5 state updated to context_built by PreprocessingActor
2025-07-02 05:38:00 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 05:38:00 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 05:55:12 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 05:55:12 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 06:12:29 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 06:12:29 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 06:17:04 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 06:17:04 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 06:31:50 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 06:31:50 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 07:48:21 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 07:48:21 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 07:52:05 - ❓ pymongo.client - 104- ERROR - MongoClient background task encountered an error:
Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 521, in _configured_socket_interface
    ssl_sock = ssl_context.wrap_socket(sock, server_hostname=host)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sock=sock,
        ^^^^^^^^^^
    ...<5 lines>...
        session=session
        ^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 1076, in _create
    self.do_handshake()
    ~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 1372, in do_handshake
    self._sslobj.do_handshake()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
TimeoutError: _ssl.c:1011: The handshake operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/mongo_client.py", line 2227, in _process_periodic_tasks
    self._topology.update_pool()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/topology.py", line 688, in update_pool
    server.pool.remove_stale_sockets(generation)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 979, in remove_stale_sockets
    conn = self.connect()
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1033, in connect
    networking_interface = _configured_socket_interface(self.address, self.opts)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 535, in _configured_socket_interface
    _raise_connection_failure(address, exc, "SSL handshake failed: ", timeout_details=details)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 141, in _raise_connection_failure
    raise NetworkTimeout(msg) from error
pymongo.errors.NetworkTimeout: SSL handshake failed: ac-epyijkm-shard-00-00.qeaymfg.mongodb.net:27017: _ssl.c:1011: The handshake operation timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 10000.0ms)
2025-07-02 08:46:06 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 08:46:06 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 10:41:45 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 10:41:45 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 11:50:52 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 11:50:52 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 13:52:59 - ❓ pymongo.client - 104- ERROR - MongoClient background task encountered an error:
Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 521, in _configured_socket_interface
    ssl_sock = ssl_context.wrap_socket(sock, server_hostname=host)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sock=sock,
        ^^^^^^^^^^
    ...<5 lines>...
        session=session
        ^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 1076, in _create
    self.do_handshake()
    ~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 1372, in do_handshake
    self._sslobj.do_handshake()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
ConnectionResetError: [Errno 54] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/mongo_client.py", line 2227, in _process_periodic_tasks
    self._topology.update_pool()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/topology.py", line 688, in update_pool
    server.pool.remove_stale_sockets(generation)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 979, in remove_stale_sockets
    conn = self.connect()
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1033, in connect
    networking_interface = _configured_socket_interface(self.address, self.opts)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 535, in _configured_socket_interface
    _raise_connection_failure(address, exc, "SSL handshake failed: ", timeout_details=details)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 149, in _raise_connection_failure
    raise AutoReconnect(msg) from error
pymongo.errors.AutoReconnect: SSL handshake failed: ac-epyijkm-shard-00-01.qeaymfg.mongodb.net:27017: [Errno 54] Connection reset by peer (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 10000.0ms)
2025-07-02 14:26:50 - ❓ pymongo.client - 104- ERROR - MongoClient background task encountered an error:
Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1033, in connect
    networking_interface = _configured_socket_interface(self.address, self.opts)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 509, in _configured_socket_interface
    sock = _create_connection(address, options)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 444, in _create_connection
    raise err
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 437, in _create_connection
    sock.connect(sa)
    ~~~~~~~~~~~~^^^^
TimeoutError: [Errno 60] Operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/mongo_client.py", line 2227, in _process_periodic_tasks
    self._topology.update_pool()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/topology.py", line 688, in update_pool
    server.pool.remove_stale_sockets(generation)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 979, in remove_stale_sockets
    conn = self.connect()
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1056, in connect
    _raise_connection_failure(self.address, error, timeout_details=details)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 141, in _raise_connection_failure
    raise NetworkTimeout(msg) from error
pymongo.errors.NetworkTimeout: ac-epyijkm-shard-00-00.qeaymfg.mongodb.net:27017: [Errno 60] Operation timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 10000.0ms)
2025-07-02 14:31:14 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 14:31:14 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 14:55:18 - ❓ pymongo.client - 104- ERROR - MongoClient background task encountered an error:
Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/mongo_client.py", line 2227, in _process_periodic_tasks
    self._topology.update_pool()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/topology.py", line 688, in update_pool
    server.pool.remove_stale_sockets(generation)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 979, in remove_stale_sockets
    conn = self.connect()
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1073, in connect
    conn.authenticate()
    ~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 526, in authenticate
    auth.authenticate(creds, self, reauthenticate=reauthenticate)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/auth.py", line 450, in authenticate
    auth_func(credentials, conn)
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/auth.py", line 355, in _authenticate_default
    return _authenticate_scram(credentials, conn, "SCRAM-SHA-1")
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/auth.py", line 96, in _authenticate_scram
    res = conn.command(source, cmd)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/helpers.py", line 47, in inner
    return func(*args, **kwargs)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 442, in command
    self._raise_connection_failure(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 414, in command
    return command(
        self,
    ...<20 lines>...
        write_concern=write_concern,
    )
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/network.py", line 198, in command
    reply = receive_message(conn, request_id)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/network_layer.py", line 759, in receive_message
    length, _, response_to, op_code = _UNPACK_HEADER(receive_data(conn, 16, deadline))
                                                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/network_layer.py", line 361, in receive_data
    raise _OperationCancelled("operation cancelled") from None
pymongo.errors._OperationCancelled: operation cancelled
2025-07-02 15:11:06 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 15:11:06 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 15:15:15 - ❓ pymongo.client - 104- ERROR - MongoClient background task encountered an error:
Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 521, in _configured_socket_interface
    ssl_sock = ssl_context.wrap_socket(sock, server_hostname=host)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sock=sock,
        ^^^^^^^^^^
    ...<5 lines>...
        session=session
        ^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 1076, in _create
    self.do_handshake()
    ~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.13.3-macos-aarch64-none/lib/python3.13/ssl.py", line 1372, in do_handshake
    self._sslobj.do_handshake()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
TimeoutError: _ssl.c:1011: The handshake operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/mongo_client.py", line 2227, in _process_periodic_tasks
    self._topology.update_pool()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/topology.py", line 688, in update_pool
    server.pool.remove_stale_sockets(generation)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 979, in remove_stale_sockets
    conn = self.connect()
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1033, in connect
    networking_interface = _configured_socket_interface(self.address, self.opts)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 535, in _configured_socket_interface
    _raise_connection_failure(address, exc, "SSL handshake failed: ", timeout_details=details)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 141, in _raise_connection_failure
    raise NetworkTimeout(msg) from error
pymongo.errors.NetworkTimeout: SSL handshake failed: ac-epyijkm-shard-00-01.qeaymfg.mongodb.net:27017: _ssl.c:1011: The handshake operation timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 10000.0ms)
2025-07-02 15:31:48 - ❓ aiogram.dispatcher - 219- ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-07-02 15:31:48 - ❓ aiogram.dispatcher - 221- WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7996644839)
2025-07-02 15:50:32 - ❓ pymongo.client - 104- ERROR - MongoClient background task encountered an error:
Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1033, in connect
    networking_interface = _configured_socket_interface(self.address, self.opts)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 509, in _configured_socket_interface
    sock = _create_connection(address, options)
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 444, in _create_connection
    raise err
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 437, in _create_connection
    sock.connect(sa)
    ~~~~~~~~~~~~^^^^
TimeoutError: [Errno 60] Operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/mongo_client.py", line 2227, in _process_periodic_tasks
    self._topology.update_pool()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/topology.py", line 688, in update_pool
    server.pool.remove_stale_sockets(generation)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 979, in remove_stale_sockets
    conn = self.connect()
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/synchronous/pool.py", line 1056, in connect
    _raise_connection_failure(self.address, error, timeout_details=details)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/zeitwahl/.venv/lib/python3.13/site-packages/pymongo/pool_shared.py", line 141, in _raise_connection_failure
    raise NetworkTimeout(msg) from error
pymongo.errors.NetworkTimeout: ac-epyijkm-shard-00-01.qeaymfg.mongodb.net:27017: [Errno 60] Operation timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 10000.0ms)
^C2025-07-02 15:51:43 - ❓ aiogram.dispatcher - 491- WARNING - Received SIGINT signal